package com.gxgz.basicdata.controller;

import java.util.List;

import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.basicdata.domain.vo.GzDzjzmlVo;
import com.gxgz.basicdata.domain.bo.GzDzjzmlBo;
import com.gxgz.basicdata.service.IGzDzjzmlService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 电子卷宗目录
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/dzjzml")
public class GzDzjzmlController extends BaseController {

    private final IGzDzjzmlService gzDzjzmlService;

    /**
     * 查询电子卷宗目录列表
     */
//    @SaCheckPermission("basicdata:dzjzml:list")
    @GetMapping("/list")
    public TableDataInfo<GzDzjzmlVo> list(GzDzjzmlBo bo, PageQuery pageQuery) {
        return gzDzjzmlService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出电子卷宗目录列表
     */
//    @SaCheckPermission("basicdata:dzjzml:export")
    @Log(title = "电子卷宗目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzDzjzmlBo bo, HttpServletResponse response) {
        List<GzDzjzmlVo> list = gzDzjzmlService.queryList(bo);
        ExcelUtil.exportExcel(list, "电子卷宗目录", GzDzjzmlVo.class, response);
    }

    /**
     * 获取电子卷宗目录详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:dzjzml:query")
    @GetMapping("/{id}")
    public R<GzDzjzmlVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzDzjzmlService.queryById(id));
    }

    /**
     * 新增电子卷宗目录
     */
//    @SaCheckPermission("basicdata:dzjzml:add")
    @Log(title = "电子卷宗目录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzDzjzmlBo bo) {
        return toAjax(gzDzjzmlService.insertByBo(bo));
    }

    /**
     * 修改电子卷宗目录
     */
//    @SaCheckPermission("basicdata:dzjzml:edit")
    @Log(title = "电子卷宗目录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzDzjzmlBo bo) {
        return toAjax(gzDzjzmlService.updateByBo(bo));
    }

    /**
     * 删除电子卷宗目录
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:dzjzml:remove")
    @Log(title = "电子卷宗目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzDzjzmlService.deleteWithValidByIds(List.of(ids), true));
    }
}
