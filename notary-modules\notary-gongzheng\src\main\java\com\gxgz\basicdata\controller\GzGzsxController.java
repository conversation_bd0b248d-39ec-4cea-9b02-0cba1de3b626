package com.gxgz.basicdata.controller;

import java.util.List;

import com.gxgz.basicdata.domain.bo.GzGzsxPzBo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblBo;
import com.gxgz.basicdata.domain.vo.GzGzsxPzVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxVo;
import com.gxgz.basicdata.domain.bo.GzGzsxBo;
import com.gxgz.basicdata.service.IGzGzsxService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 基础数据-公证事项
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsx")
public class GzGzsxController extends BaseController {

    private final IGzGzsxService gzGzsxService;

    /**
     * 查询基础数据-公证事项列表
     */
//    @SaCheckPermission("basicdata:gzsx:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxVo> list(GzGzsxBo bo, PageQuery pageQuery) {
        return gzGzsxService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出基础数据-公证事项列表
     */
//    @SaCheckPermission("basicdata:gzsx:export")
    @Log(title = "基础数据-公证事项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxBo bo, HttpServletResponse response) {
        List<GzGzsxVo> list = gzGzsxService.queryList(bo);
        ExcelUtil.exportExcel(list, "基础数据-公证事项", GzGzsxVo.class, response);
    }

    /**
     * 获取基础数据-公证事项详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:gzsx:query")
    @GetMapping("/{id}")
    public R<GzGzsxVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxService.queryById(id));
    }

    /**
     * 新增基础数据-公证事项
     */
//    @SaCheckPermission("basicdata:gzsx:add")
    @Log(title = "基础数据-公证事项", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxBo bo) {
        return toAjax(gzGzsxService.insertByBo(bo));
    }

    /**
     * 修改基础数据-公证事项
     */
//    @SaCheckPermission("basicdata:gzsx:edit")
    @Log(title = "基础数据-公证事项", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxBo bo) {
        return toAjax(gzGzsxService.updateByBo(bo));
    }

    /**
     * 删除基础数据-公证事项
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:gzsx:remove")
    @Log(title = "基础数据-公证事项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxService.deleteWithValidByIds(List.of(ids), true));
    }


//    @SaCheckPermission("basicdata:gzsx:list")
    @GetMapping("/listTree")
    public R listTree(GzGzsxBo bo) {
        List<GzGzsxVo> gzGzsxVoList = gzGzsxService.listTree(bo);
        return R.ok("ok", gzGzsxVoList);
    }

//    @SaCheckPermission("basicdata:gzsx:list")
    @GetMapping("/list/exclude/{gzsId}")
    public R listExclude(@PathVariable(value = "id", required = false) Long id) {
        List<GzGzsxVo> gzGzsxVoList = gzGzsxService.listExclude(id);
        return R.ok("ok", gzGzsxVoList);
    }

    @GetMapping("/listTreeByZxbl")
    public R listTreeByZxbl(GzGzsxZxblBo bo) {
        List<GzGzsxVo> gzGzsxVoList = gzGzsxService.listTreeByZxbl(bo);
        return R.ok("ok", gzGzsxVoList);
    }
}
