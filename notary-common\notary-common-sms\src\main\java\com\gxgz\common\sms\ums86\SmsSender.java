package com.gxgz.common.sms.ums86;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.Charset;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/*
*
public static void main(String[] args) {
    // 初始化短信发送器
    SmsConfig config = new SmsConfig(
        "http://121.237.181.142:9111/sms/Api/Send.do",
        "244531",    // SpCode
        "xgzywxt",    // LoginName
        "23ecf66bcf386814de33a16f1c1b3b4e"     // Password
    );
    SmsSender sender = new SmsSender(config);

    // 发送短信
    SmsResult result = sender.send("18577826949", "您的验证码为12345678该验证码5分钟内有效，请勿泄露于他人。");

    // 处理结果
    System.out.println("发送结果: " + result);
    if (result.isSuccess()) {
        System.out.println("发送成功，任务ID: " + result.getTaskId());
    } else {
        System.out.println("发送失败: " + result.getDescription());
    }
}
*
* */
@Component
public class SmsSender {
    @Autowired
    private Ums86SmsConfig config;

//    public SmsSender(SmsConfig config) {
//        this.config = config;
//    }

    /**
     * 发送短信
     * @param phoneNumber 接收短信的手机号码
     * @param messageContent 短信内容
     * @return 短信发送结果对象
     */
    public SmsResult send(String phoneNumber, String messageContent) {
        try {
            // 创建HTTP客户端
            HttpClient httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();

            // 构建表单数据
            StringBuilder formData = new StringBuilder();
            formData.append("SpCode=").append(config.getSpCode());
            formData.append("&LoginName=").append(config.getLoginName());
            formData.append("&Password=").append(config.getPassword());
            formData.append("&MessageContent=").append(messageContent);
            formData.append("&UserNumber=").append(phoneNumber);
            formData.append("&SerialNumber=");
            formData.append("&ScheduleTime=");
            formData.append("&f=1");

            // 构建POST请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(config.getApiUrl()))
                .header("Content-Type", "application/x-www-form-urlencoded; charset=gbk")
                .POST(HttpRequest.BodyPublishers.ofString(formData.toString(),
                    Charset.forName("GB2312")))
                .timeout(Duration.ofSeconds(10))
                .build();

            // 发送请求并获取响应
            HttpResponse<String> response = httpClient.send(
                request,
                HttpResponse.BodyHandlers.ofString(Charset.forName("GB2312"))
            );

            // 解析响应结果并返回
            return parseSmsResult(response.body());
        } catch (Exception e) {
            e.printStackTrace();
            return new SmsResult(-1, "发送过程发生异常: " + e.getMessage(), "", "", "");
        }
    }

    /**
     * 解析短信发送结果字符串
     * @param resultString 原始结果字符串
     * @return 解析后的SmsResult对象
     */
    private SmsResult parseSmsResult(String resultString) {
        Map<String, String> resultMap = new HashMap<>();

        // 分割键值对
        String[] pairs = resultString.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0], keyValue[1]);
            } else if (keyValue.length == 1) {
                resultMap.put(keyValue[0], "");
            }
        }

        // 提取各个字段
        int resultCode = Integer.parseInt(resultMap.getOrDefault("result", "-1"));
        String description = resultMap.getOrDefault("description", "未知结果");
        String taskId = resultMap.getOrDefault("taskid", "");
        String failList = resultMap.getOrDefault("faillist", "");
        String taskId2 = resultMap.getOrDefault("task_id", "");

        return new SmsResult(resultCode, description, taskId, failList, taskId2);
    }


}
