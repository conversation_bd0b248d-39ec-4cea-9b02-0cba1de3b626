package com.gxgz.guichain.utils;

import com.gxgz.common.core.enums.OnChainClassifyEnum;
import com.gxgz.common.core.utils.SpringUtils;
import com.gxgz.guichain.domain.bo.GzGxbassLogBo;
import com.gxgz.guichain.service.IGzGxbassLogService;

public class OnChainUtils {
    /**
     * 数据上链日志
     *
     * @param onChainClassifyEnum 业务分类
     * @param bizId               业务ID
     * @param content             上链数据
     * @param channelId           加密通道ID
     * @param txHash              上链哈希
     */
    public static void addOnChainLog(OnChainClassifyEnum onChainClassifyEnum, Long bizId, String content,String chainDec, String channelId, String txHash) {
        GzGxbassLogBo bo = new GzGxbassLogBo();
        bo.setBizId(bizId.toString());
        bo.setChainDec(chainDec);
        bo.setBizClassify(onChainClassifyEnum.getValue());
        bo.setTxHash(txHash);
        bo.setChainData(content);
        bo.setChainType(2L);
        bo.setChannelId(channelId);
        IGzGxbassLogService service = SpringUtils.getBean(IGzGxbassLogService.class);
        service.insertByBo(bo);
    }

    /**
     * 文件上链日志
     *
     * @param onChainClassifyEnum 业务分类
     * @param bizId               业务ID
     * @param ossId               文件ossid
     * @param channelId           加密通道ID
     * @param txHash              上链哈希
     */
    public static void addOnChainFileLog(OnChainClassifyEnum onChainClassifyEnum, Long bizId, Long ossId,String chainDec, String channelId, String txHash) {
        GzGxbassLogBo bo = new GzGxbassLogBo();
        bo.setBizId(bizId.toString());
        bo.setBizClassify(onChainClassifyEnum.getValue());
        bo.setTxHash(txHash);
        bo.setChainType(1L);
        bo.setChainDec(chainDec);
        bo.setChannelId(channelId);
        bo.setOssId(ossId);
        IGzGxbassLogService service = SpringUtils.getBean(IGzGxbassLogService.class);
        service.insertByBo(bo);
    }
}
