package com.gxgz.basicdata.domain;

import com.gxgz.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 在线办理配置对象 gz_gzsx_zxbl_config
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gz_gzsx_zxbl_config")
public class GzGzsxZxblConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 公证事项名称（冗余字段）
     */
    private String gzsxName;

    /**
     * 事项编号（冗余字段）
     */
    private String gzsxCode;

    /**
     * 公证事项ID（基于GzsxVO中id）
     */
    private Long gzsxId;

    /**
     * 是否在线办理（1是/0否）
     */
    private Long isOnlineHandle;

    /**
     * 是否启用办理（1是/0否）
     */
    private Long isEnableHandle;

    /**
     * 申办材料ID（多个以逗号分隔）
     */
    private String materialIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识：0-正常，1-删除
     */
    @TableLogic
    private String delFlag;


}
