package com.gxgz.common.sms.ums86;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Data
@Component
@ConfigurationProperties(prefix = "sms")
public class Ums86SmsConfig {
    private String apiUrl;       // 短信API接口地址
    private String spCode;       // 服务代码
    private String loginName;    // 登录用户名
    private String password;     // 登录密码

//    // 构造函数初始化配置
//    public SmsConfig(String apiUrl, String spCode, String loginName, String password) {
//        this.apiUrl = apiUrl;
//        this.spCode = spCode;
//        this.loginName = loginName;
//        this.password = password;
//    }
}
