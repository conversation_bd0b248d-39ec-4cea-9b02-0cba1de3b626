package com.gxgz.system.domain.bo;

import com.gxgz.system.domain.GzSmsRecord;
import com.gxgz.common.mybatis.core.domain.BaseEntity;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 短信发送日志业务对象 gz_sms_record
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GzSmsRecord.class, reverseConvertGenerate = false)
public class GzSmsRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 公证卷宗ID
     */
    @NotNull(message = "公证卷宗ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long gzjzId;

    /**
     * 当事人ID
     */
    @NotBlank(message = "当事人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dsrId;

    /**
     * 当事人姓名
     */
    @NotBlank(message = "当事人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dsrName;

    /**
     * 当事人联系电话
     */
    @NotBlank(message = "当事人联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dsrPhone;

    /**
     * 短信内容
     */
    @NotBlank(message = "短信内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String smsContent;

    /**
     * 发送时间(精确到秒)
     */
    @NotNull(message = "发送时间(精确到秒)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date sendTime;

    /**
     * 发送状态
     */
    @NotNull(message = "发送状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sendStatus;

    /**
     * 发送反馈时间(精确到秒)
     */
    private Date feedbackTime;

    /**
     * 发送反馈结果(失败原因等)
     */
    private String feedbackResult;

    /**
     * 备注信息
     */
    private String remark;
    private String gzjzBh;

}
