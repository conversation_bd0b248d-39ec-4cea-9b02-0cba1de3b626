package com.gxgz.common.core.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor // 关键：生成无参构造器
@AllArgsConstructor
public class ParamsDTO {

    private String type;
    private String name;
    private String value;
    /**
     * 0 默认 {}
     * 1 自定义
     */
    private Integer labelType;

    /**
     * 表格数据
     */
    private List<ParamsTableDTO> tableDTOList;
    private Map<String, ParamsDTO> otherWordParams;


}
