package com.gxgz.common.pageoffice.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一文档操作视图对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedDocumentVO {

    /**
     * 文档ID
     */
    private String documentId;

    /**
     * 文档名称
     */
    private String documentName;

    /**
     * 文档类型
     */
    private String documentType;

    /**
     * 操作类型
     */
    private String action;

    /**
     * 编辑模式
     */
    private String editMode;

    /**
     * PageOffice控件HTML代码
     */
    private String htmlCode;

    /**
     * 控件ID
     */
    private String controlId;

    /**
     * 编辑URL
     */
    private String editUrl;

    /**
     * 保存URL
     */
    private String saveUrl;

    /**
     * 删除URL
     */
    private String deleteUrl;

    /**
     * JavaScript代码
     */
    private String jsCode;

    /**
     * 文档状态
     */
    private String status;

    /**
     * 文档路径（保存后的路径）
     */
    private String documentPath;

    /**
     * 文档网络地址
     */
    private String documentUrl;

    /**
     * 文档大小（字节）
     */
    private Long fileSize;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModified;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否启用印章功能
     */
    private Boolean enableSeal;

    /**
     * 是否启用修订模式
     */
    private Boolean enableRevision;

    /**
     * 操作结果码
     */
    private Integer code;

    /**
     * 操作结果消息
     */
    private String message;

    /**
     * 扩展信息
     */
    private Map<String, Object> extendedInfo;
    /**
     * 文档保存地址
     */
    private String savePath;

    /**
     * 回调地址
     */
    private String callbackUrl;
    /**
     * 业务类型
     */
    private String ywTyp;
    /**
     * 附件类别 对应 ZmclxxTypeEnum
     */
    private String fjlb;

    /**
     * 生成 保存地址
     */
    private OssVo generateOss;
}
