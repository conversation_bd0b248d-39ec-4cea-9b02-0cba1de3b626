package com.gxgz.guichain.service.impl;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.core.utils.MapstructUtils;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.gxgz.guichain.domain.bo.GzGxbassLogBo;
import com.gxgz.guichain.domain.vo.GzGxbassLogVo;
import com.gxgz.guichain.domain.GzGxbassLog;
import com.gxgz.guichain.mapper.GzGxbassLogMapper;
import com.gxgz.guichain.service.IGzGxbassLogService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 公证-桂链-上链日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@RequiredArgsConstructor
@Service
public class GzGxbassLogServiceImpl implements IGzGxbassLogService {

    private final GzGxbassLogMapper baseMapper;

    /**
     * 查询公证-桂链-上链日志
     *
     * @param id 主键
     * @return 公证-桂链-上链日志
     */
    @Override
    public GzGxbassLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询公证-桂链-上链日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 公证-桂链-上链日志分页列表
     */
    @Override
    public TableDataInfo<GzGxbassLogVo> queryPageList(GzGxbassLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GzGxbassLog> lqw = buildQueryWrapper(bo);
        Page<GzGxbassLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的公证-桂链-上链日志列表
     *
     * @param bo 查询条件
     * @return 公证-桂链-上链日志列表
     */
    @Override
    public List<GzGxbassLogVo> queryList(GzGxbassLogBo bo) {
        LambdaQueryWrapper<GzGxbassLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GzGxbassLog> buildQueryWrapper(GzGxbassLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GzGxbassLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GzGxbassLog::getId);
        lqw.like(StringUtils.isNotBlank(bo.getChainDec()), GzGxbassLog::getChainDec, bo.getChainDec());
        lqw.like(StringUtils.isNotBlank(bo.getChainData()), GzGxbassLog::getChainData, bo.getChainData());
        lqw.eq(bo.getOssId() != null, GzGxbassLog::getOssId, bo.getOssId());
        lqw.eq(bo.getChainType() != null, GzGxbassLog::getChainType, bo.getChainType());
        lqw.like(StringUtils.isNotBlank(bo.getChannelId()), GzGxbassLog::getChannelId, bo.getChannelId());
        lqw.eq(bo.getEncryptMode() != null, GzGxbassLog::getEncryptMode, bo.getEncryptMode());
        lqw.like(StringUtils.isNotBlank(bo.getTxHash()), GzGxbassLog::getTxHash, bo.getTxHash());
        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), GzGxbassLog::getBizId, bo.getBizId());
        lqw.eq(StringUtils.isNotBlank(bo.getBizClassify()), GzGxbassLog::getBizClassify, bo.getBizClassify());
        return lqw;
    }

    /**
     * 新增公证-桂链-上链日志
     *
     * @param bo 公证-桂链-上链日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GzGxbassLogBo bo) {
        GzGxbassLog add = MapstructUtils.convert(bo, GzGxbassLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改公证-桂链-上链日志
     *
     * @param bo 公证-桂链-上链日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GzGxbassLogBo bo) {
        GzGxbassLog update = MapstructUtils.convert(bo, GzGxbassLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GzGxbassLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除公证-桂链-上链日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
