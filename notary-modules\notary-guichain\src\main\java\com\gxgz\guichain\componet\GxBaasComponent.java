package com.gxgz.guichain.componet;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gxgz.common.core.constant.Constants;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.redis.utils.RedisUtils;
import com.gxgz.guichain.config.GxBaasConfig;
import com.gxgz.guichain.domain.bo.EncryptBo;
import com.gxgz.guichain.domain.bo.GxBassDataBo;
import com.gxgz.guichain.domain.bo.GxSignBo;
import com.gxgz.guichain.domain.vo.GxBassDataVo;
import com.gxgz.guichain.domain.vo.GxBassFileVo;
import com.gxgz.guichain.domain.vo.GxDetailVo;
import com.gxgz.guichain.domain.vo.GxSignVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RequiredArgsConstructor
@Component
@Slf4j
public class GxBaasComponent {
    private final GxBaasConfig gxBaasConfig;

    /**
     * 数据存证
     * @param chainData 存证数据
     * @param chainDataDesc 存证描述
     * @return  上链交易哈希
     */
    public String uploadData(String bizId,String chainData,String chainDataDesc){
        Map<String,Object> map = new HashMap<>();
        map.put("bizId",bizId ); //业务id
        map.put("bizType", 2); //业务类型 2公共存证服务
        map.put("encryptMode", 2); //加密方式 1 授权加密 0 不加密 2 通道加密
        map.put("data", chainData); //需要上链的数据
        map.put("channelId", gxBaasConfig.getChannelId()); //需要上链的数据
        map.put("proofDataDesc", chainDataDesc); //存证数据描述
        String token = getToken();
        String requestParams = JSONUtil.toJsonStr(map);
        String result = HttpRequest.post(gxBaasConfig.getUploadDataUrl())
            .header("authorization",token)
            .header("content-type","application/json")
            .body(requestParams).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        String txHash = jsonObject.getStr("tx_hash");
        return txHash;
    }


    /**
     * 数据存证列表查询
     * @param bo
     * @return
     */
    public TableDataInfo<GxBassDataVo> getProofData(GxBassDataBo bo){

        String token = getToken();
        String requestParams = JSONUtil.toJsonStr(bo);
        String result = HttpRequest.get(gxBaasConfig.getGetProofDataUrl())
            .header("authorization",token)
            .header("content-type","application/json")
            .body(requestParams).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        Integer total = data.getInt("total");
        List<GxBassDataVo> list = data.getBeanList("list", GxBassDataVo.class);
        Page<GxBassDataVo> page = new Page<>();
        page.setRecords(list);
        page.setTotal(total);
        return TableDataInfo.build(page);
    }


    /**
     * 数据存证解密
     * @param bo
     * @return
     */
    public Object getDecryptData(EncryptBo bo){
        String token = getToken();
        String requestParams = JSONUtil.toJsonStr(bo);
        String result = HttpRequest.post(gxBaasConfig.getDecryptDataUrl())
            .header("authorization",token)
            .header("content-type","application/json")
            .body(requestParams).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        Object data = jsonObject.getJSONObject("data").toBean(Object.class);
        return data;
    }

    /**
     *
     * @param file 存证文件
     * @param chainDataDesc  存证描述
     * @return 上链交易哈希
     */
    public String uploadFile(File file, String chainDataDesc){
        String bizId = RandomUtil.randomNumbers(18);
        String token = getToken();
        String result = HttpRequest.post(gxBaasConfig.getUploadFileUrl())
            .header("authorization",token)
            .header("content-type","form-data")
            .form("bizId",bizId)
            .form("bizType",2)
            .form("encryptMode",2)
            .form("channelId",gxBaasConfig.getChannelId())
            .form("file",file)
            .form("proofDataDesc",chainDataDesc)
            .execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        String txHash = jsonObject.getStr("tx_hash");
        return txHash;
    }

    /**
     * 文件存证列表查询
     * @param bo
     * @return
     */
    public TableDataInfo<GxBassFileVo> getFileData(GxBassDataBo bo){

        String token = getToken();
        String requestParams = JSONUtil.toJsonStr(bo);
        String result = HttpRequest.post(gxBaasConfig.getGetFileDataUrl())
            .header("authorization",token)
            .header("content-type","application/json")
            .body(requestParams).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        Integer total = data.getInt("total");
        List<GxBassFileVo> list = data.getBeanList("list", GxBassFileVo.class);
        Page<GxBassFileVo> page = new Page<>();
        page.setRecords(list);
        page.setTotal(total);
        return TableDataInfo.build(page);
    }


    /**
     * 获取交易详情
     * @param hash 交易hash
     * @return
     */
    public GxDetailVo getDetail(String hash){
       String token = getToken();
       StringBuffer parma = new StringBuffer();
       parma.append(gxBaasConfig.getGetDetailUrl()).append("?hash=").append(hash);
        String result = HttpRequest.get(parma.toString())
            .header("authorization",token)
            .header("content-type","application/json")
            .execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        GxDetailVo gxDetailVo = jsonObject.getBean("data", GxDetailVo.class);
        return gxDetailVo;
    }

    /**
     * 数据验证
     * @param bo
     * @return
     */
    public GxSignVo signCheck(GxSignBo bo){
        String token = getToken();
        String requestParams = JSONUtil.toJsonStr(bo);
        String result = HttpRequest.post(gxBaasConfig.getSignCheckUrl())
            .header("authorization",token)
            .header("content-type","application/json")
            .body(requestParams).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        GxSignVo gxSignVo = jsonObject.getBean("data",GxSignVo.class);

        return gxSignVo;
    }

    /**
     * 预览证书
     * @param hash 交易hash
     * @return
     */
    public String getCertificate(String hash){
        String token = getToken();
        StringBuffer parma = new StringBuffer();
        parma.append(gxBaasConfig.getGetCertificateUrl()).append("?txHash=").append(hash);
        String result = HttpRequest.get(parma.toString())
            .header("authorization",token)
            .header("content-type","application/json")
            .execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        String templateBackGroundPath = jsonObject.getJSONObject("data").getStr("templateBackGroundPath");
        return templateBackGroundPath;
    }

    /**
     * 下载证书
     * @param hash 交易hash
     * @return
     */
    public byte[] downloadCertificate(String hash){
        String token = getToken();
        StringBuffer parma = new StringBuffer();
        parma.append(gxBaasConfig.getDownloadCertificateUrl()).append("?txHash=").append(hash);
      /*  String result = HttpRequest.get(parma.toString())
            .header("authorization",token)
            .header("content-type","application/json")
            .execute().body();
        String filePath = "C:\\Users\\<USER>\\Desktop\\11\\桂链出证.pdf";
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(result.getBytes());
            System.out.println("数据已写入到：" + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return result.getBytes();*/

        HttpResponse body = HttpUtil.createGet(parma.toString())
            .header("authorization",token)
            .header("content-type","application/json")
            .execute();

        return body.bodyBytes();

    }

    /**
     * 文件存证解密
     * @param txHash
     * @return
     */
    public byte[] getDecryptFile(String txHash){
        String token = getToken();
        StringBuffer parma = new StringBuffer();
        parma.append(gxBaasConfig.getDecryptFileUrl()).append("?txHash=").append(txHash);
        HttpResponse body = HttpUtil.createGet(parma.toString())
            .header("authorization",token)
            .header("content-type","application/json")
            .execute();
        return body.bodyBytes();
    }



    /**
     * 获取token
     * @return
     */
    public  String getToken(){
        String token = RedisUtils.getCacheObject(Constants.GXBASS_TOKEN);
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        Map<String,String> map = new HashMap<>();
        map.put("apiKey", gxBaasConfig.getApiKey());
        map.put("apiSecret", gxBaasConfig.getApiSecret());
        String requestParams = JSONUtil.toJsonStr(map);
        String result = HttpRequest.post(gxBaasConfig.getLoginUrl())
           .body(requestParams).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!checkRequest(jsonObject)){
            return null;
        }
        token = jsonObject.getJSONObject("data").getStr("access_token");
        Integer expireTime = jsonObject.getJSONObject("data").getInt("expire_time");
        RedisUtils.setCacheObject(Constants.GXBASS_TOKEN, token, Duration.ofMillis(expireTime));
        return token;
    }

    /**
     * 检查请求是否成功
     * @param jsonObject
     * @return
     */
    public boolean checkRequest(JSONObject jsonObject) {
        Integer code = jsonObject.getInt("code");
        if (!Objects.equals(code, R.SUCCESS)){
            log.error("桂链请求接口失败：{}",jsonObject.getStr("msg"));
            return false;
        }
        return true;
    }



}
