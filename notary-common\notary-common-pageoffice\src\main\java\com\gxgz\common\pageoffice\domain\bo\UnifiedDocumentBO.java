package com.gxgz.common.pageoffice.domain.bo;

import com.gxgz.common.core.domain.dto.ParamsDTO;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * 统一文档操作业务对象
 *
 * <AUTHOR>
 */
@Data
public class UnifiedDocumentBO {

    /**
     * 文档ID
     */
    @NotBlank(message = "文档ID不能为空")
    private String documentId;

    /**
     * 文档名称
     */
    @NotBlank(message = "文档名称不能为空")
    private String documentName;

    /**
     * 文档路径（本地路径）
     */
    private String documentPath;

    /**
     * 文档网络地址（优先级高于documentPath）
     */
    private String documentUrl;

    /**
     * 文档类型（可选，如不提供将自动识别）
     */
    private String documentType;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    private String action;

    /**
     * 编辑模式
     */
    private String editMode = "edit";

    /**
     * 用户名
     */
//    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否启用修订模式
     */
    private Boolean enableRevision = false;

    /**
     * 是否启用印章功能
     */
    private Boolean enableSeal = false;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 控件宽度
     */
    private String width = "100%";

    /**
     * 控件高度
     */
    private String height = "600px";

    /**
     * 自定义参数（用于文档内参数嵌入）
     */
    private Map<String, ParamsDTO> customParams;

    /**
     * 文档描述
     */
    private String description;

    /**
     * 是否自动保存
     */
    private Boolean autoSave = false;

    /**
     * 自动保存间隔（秒）
     */
    private Integer autoSaveInterval = 300;

    /**
     * 是否启用协同编辑
     */
    private Boolean enableCollaboration = false;

    /**
     * 扩展属性
     */
    private Map<String, Object> extendedProperties;

    /**
     * 文档保存地址
     */
    private String savePath;
    /**
     * 业务类型
     */
    private String ywTyp;
    /**
     * 附件类别 对应 ZmclxxTypeEnum
     */
    private String fjlb;

    /**
     * 保存 文档类型  可选 word 、excel 、pdf 、ppt
     * */
    private String saveDocumentType;
}
