package com.gxgz.gongzheng.controller;

import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.gongzheng.domain.bo.GzZxdxxZjmcBo;
import com.gxgz.gongzheng.domain.vo.GzZxdxxZjmcVo;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页 统计
 */
@Hidden
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/gongzheng/home")
public class HomeController extends BaseController {

    /**
     * 办证数：
     * 按今日、本周、本月时间维度统计，所有的不限状态等；
     * 统计 gz_gzjz_jbxx 按 slrq 字段 分组统计 今日、本周、本月 总数
     * * 待办事项 总数：
     *      * 受理中卷宗数：gz_gzjz_jbxx.lczt=[01,02,03,04] 且 gz_gzjz_jbxx.sfzf=0  且 gz_gzjz_jbxx.jyfs=10
     *      * 待收费卷宗数：gz_gzjz_jbxx.lczt=[01,02,03,04] 且 gz_gzjz_jbxx.sfzf=0  且 gz_gzjz_jbxx.jyfs=10 且 gz_gzjz_gzsx_sfxx.sfzt=1
     *      * 待审批卷宗数：gz_gzjz_jbxx.lczt=05 且 gz_gzjz_jbxx.sfzf=0  且 gz_gzjz_jbxx.jyfs=10
     *      * 待制证卷宗数：gz_gzjz_jbxx.lczt=06 且 gz_gzjz_jbxx.sfzf=0  且 gz_gzjz_jbxx.jyfs=10
     *      * 待发证卷宗数：gz_gzjz_jbxx.lczt=07 且 gz_gzjz_jbxx.sfzf=0  且 gz_gzjz_jbxx.jyfs=10
     *      * 待归档案卷数：gz_gzjz_jbxx.lczt=08 且 gz_gzjz_jbxx.sfzf=0  且 gz_gzjz_jbxx.jyfs=10
     *      * 待翻译数：gz_gzjz_wjccxx.sf_fy = 1 且 gz_gzjz_wjccxx.fyzt = 0
     *      * 待校对数：gz_gzjz_wjccxx.sf_fy = 1 且 gz_gzjz_wjccxx.fyzt = 1
     *      * 待审批特殊流程卷宗数: gz_tslc_sqb.tslc_zt = 1
     */


    /**
     * 公证费统计：
     * 按根据卷宗主表 gz_gzjz_jbxx 、收费子表 gz_gzjz_gzsx_sfxx
     * 按今日、本周、本月时间维度统计 fyss 实收（缴费）金额 总和
     */

}
