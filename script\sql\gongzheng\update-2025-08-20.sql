
--- 2025-08-21 ： 已更新至 gongzheng_prod。

-- 表结构更新
CREATE TABLE "public"."gz_gxbass_log" (
  "id" int8 NOT NULL,
  "chain_dec" varchar(255) COLLATE "pg_catalog"."default",
  "chain_data" text COLLATE "pg_catalog"."default",
  "oss_id" int8,
  "chain_type" int4,
  "channel_id" varchar(255) COLLATE "pg_catalog"."default",
  "encrypt_mode" int4,
  "tx_hash" varchar(255) COLLATE "pg_catalog"."default",
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "create_dept" int8,
  "create_by" int8,
  "create_time" timestamp(6),
  "update_by" int8,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "tenant_id" varchar(20) COLLATE "pg_catalog"."default" DEFAULT '000000'::character varying,
  "biz_id" varbit(64),
  "biz_classify" varchar(64) COLLATE "pg_catalog"."default",
  CONSTRAINT "gz_gxbass_log_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_gxbass_log" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_gxbass_log"."chain_dec" IS '上链描述';

COMMENT ON COLUMN "public"."gz_gxbass_log"."chain_data" IS '上链数据';

COMMENT ON COLUMN "public"."gz_gxbass_log"."oss_id" IS '上链文件';

COMMENT ON COLUMN "public"."gz_gxbass_log"."chain_type" IS '存证类型 1文件存证 2 数据存证';

COMMENT ON COLUMN "public"."gz_gxbass_log"."channel_id" IS '虚拟通道ID，非虚拟通道加密方式返回空';

COMMENT ON COLUMN "public"."gz_gxbass_log"."encrypt_mode" IS '加密方式';

COMMENT ON COLUMN "public"."gz_gxbass_log"."tx_hash" IS '交易哈希';

COMMENT ON COLUMN "public"."gz_gxbass_log"."del_flag" IS '删除标志（0代表存在 1代表删除）';

COMMENT ON COLUMN "public"."gz_gxbass_log"."create_dept" IS '创建部门';

COMMENT ON COLUMN "public"."gz_gxbass_log"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_gxbass_log"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_gxbass_log"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_gxbass_log"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_gxbass_log"."remark" IS '备注';

COMMENT ON COLUMN "public"."gz_gxbass_log"."tenant_id" IS '租户编号';

COMMENT ON COLUMN "public"."gz_gxbass_log"."biz_id" IS '业务ID';

COMMENT ON COLUMN "public"."gz_gxbass_log"."biz_classify" IS '业务分类';

COMMENT ON TABLE "public"."gz_gxbass_log" IS '公证-桂链-上链日志';

ALTER TABLE "public"."gz_gzjz_fq" ALTER COLUMN "ht_je" TYPE varchar(32) COLLATE "pg_catalog"."default" USING "ht_je"::varchar(32);

ALTER TABLE "public"."gz_gzjz_fq" ALTER COLUMN "ht_jkll" TYPE varchar(32) COLLATE "pg_catalog"."default" USING "ht_jkll"::varchar(32);

ALTER TABLE "public"."gz_gzjz_jd" ALTER COLUMN "jkje" TYPE varchar(32) COLLATE "pg_catalog"."default" USING "jkje"::varchar(32);

ALTER TABLE "public"."gz_gzjz_jd" ALTER COLUMN "dsr_list" TYPE text COLLATE "pg_catalog"."default" USING "dsr_list"::text;



-- 数据更新
INSERT INTO "public"."sys_dict_data" ("dict_code", "tenant_id", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958012822143647746, '000000', 0, '不加密', '0', 'gz_chain_encrypt_mode', '', 'primary', 'N', 100, 1, '2025-08-20 11:46:49.587', 1, '2025-08-20 11:46:49.587', '');

INSERT INTO "public"."sys_dict_data" ("dict_code", "tenant_id", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958012847624044545, '000000', 0, '授权加密', '1', 'gz_chain_encrypt_mode', '', 'primary', 'N', 100, 1, '2025-08-20 11:46:55.661', 1, '2025-08-20 11:46:55.661', '');

INSERT INTO "public"."sys_dict_data" ("dict_code", "tenant_id", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958012875851710466, '000000', 0, '通道加密', '2', 'gz_chain_encrypt_mode', '', 'primary', 'N', 100, 1, '2025-08-20 11:47:02.381', 1, '2025-08-20 11:47:02.381', '');

INSERT INTO "public"."sys_dict_data" ("dict_code", "tenant_id", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958013086888116225, '000000', 0, '文件存证', '1', 'gz_chain_chain_type', '', 'primary', 'N', 100, 1, '2025-08-20 11:47:52.705', 1, '2025-08-20 11:47:52.705', '');

INSERT INTO "public"."sys_dict_data" ("dict_code", "tenant_id", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958013112913772546, '000000', 2, '数据存证', '2', 'gz_chain_chain_type', '', 'primary', 'N', 100, 1, '2025-08-20 11:47:58.904', 1, '2025-08-20 11:47:58.904', '');

UPDATE "public"."sys_dict_data" SET "tenant_id" = '000000', "dict_sort" = 90, "dict_label" = '未提交', "dict_value" = '9', "dict_type" = 'gz_tslc_zt', "css_class" = '', "list_class" = 'info', "is_default" = 'N', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-07-02 13:38:01', "update_by" = 1954948962406567938, "update_time" = '2025-08-14 10:22:52.75', "remark" = '' WHERE "dict_code" = 1940283800888700930;

INSERT INTO "public"."sys_dict_type" ("dict_id", "tenant_id", "dict_name", "dict_type", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958012491607326721, '000000', '上链-业务分类', 'gz_chain_biz_classify', 100, 1, '2025-08-20 11:45:30.782', 1, '2025-08-20 11:45:30.782', '');

INSERT INTO "public"."sys_dict_type" ("dict_id", "tenant_id", "dict_name", "dict_type", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958012682775314433, '000000', '上链-存证类型', 'gz_chain_chain_type', 100, 1, '2025-08-20 11:46:16.359', 1, '2025-08-20 11:46:16.359', '');

INSERT INTO "public"."sys_dict_type" ("dict_id", "tenant_id", "dict_name", "dict_type", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958012790917054466, '000000', '上链-加密方式', 'gz_chain_encrypt_mode', 100, 1, '2025-08-20 11:46:42.143', 1, '2025-08-20 11:46:42.143', '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1956600062713360386, '流程调度', 1936434006321528833, 40, 'processScheduling', 'gongzheng/bzfz/processScheduling/list', NULL, '1', '0', 'C', '0', '0', NULL, 'list', 100, 1, '2025-08-16 14:13:01', 1, '2025-08-16 14:18:25.473', '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958006277251100673, '桂链-上链日志', 108, 10, 'gxbassLog', 'guichain/gxbassLog/index', NULL, '1', '0', 'C', '0', '0', 'guichain:gxbassLog:list', 'list', 103, 1, '2025-08-20 11:25:20', 1, '2025-08-20 11:30:33.27', '公证-桂链-上链日志菜单');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958006277251100674, '公证-桂链-上链日志查询', 1958006277251100673, 1, '#', '', NULL, '1', '0', 'F', '0', '0', 'guichain:gxbassLog:query', '#', 103, 1, '2025-08-20 11:25:20.797119', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958006277251100675, '公证-桂链-上链日志新增', 1958006277251100673, 2, '#', '', NULL, '1', '0', 'F', '0', '0', 'guichain:gxbassLog:add', '#', 103, 1, '2025-08-20 11:25:20.897238', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958006277251100676, '公证-桂链-上链日志修改', 1958006277251100673, 3, '#', '', NULL, '1', '0', 'F', '0', '0', 'guichain:gxbassLog:edit', '#', 103, 1, '2025-08-20 11:25:21.006986', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958006277251100677, '公证-桂链-上链日志删除', 1958006277251100673, 4, '#', '', NULL, '1', '0', 'F', '0', '0', 'guichain:gxbassLog:remove', '#', 103, 1, '2025-08-20 11:25:21.09721', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958006277251100678, '公证-桂链-上链日志导出', 1958006277251100673, 5, '#', '', NULL, '1', '0', 'F', '0', '0', 'guichain:gxbassLog:export', '#', 103, 1, '2025-08-20 11:25:21.197131', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1958065465033175041, '证据补录', 1930828700166221826, 100, 'zjbl/index', 'gongzheng/gongzheng/zjbl/index', NULL, '1', '1', 'C', '0', '0', 'gz:zjbl:query', 'list', 100, 1, '2025-08-20 15:16:00', 1, '2025-08-20 15:21:29.879', '');

UPDATE "public"."sys_menu" SET "menu_name" = '受理', "parent_id" = 1930828700166221826, "order_num" = 1, "path" = 'sl', "component" = 'gongzheng/gongzheng/sl/sl_list', "query_param" = NULL, "is_frame" = '1', "is_cache" = '1', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'gz:sl:query', "icon" = 'edit', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-17 20:40:34', "update_by" = 1, "update_time" = '2025-08-13 15:19:41.546', "remark" = '' WHERE "menu_id" = 1934954320773062658;

UPDATE "public"."sys_menu" SET "menu_name" = '收费', "parent_id" = 1930829679079026689, "order_num" = 1, "path" = 'cwgaSf', "component" = 'gongzheng/cw/index', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'cwgl:sf:query', "icon" = '', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-19 16:05:53', "update_by" = 1, "update_time" = '2025-08-13 15:19:19.947', "remark" = '' WHERE "menu_id" = 1935609971230871554;

UPDATE "public"."sys_menu" SET "menu_name" = '归档', "parent_id" = 1930828700166221826, "order_num" = 10, "path" = 'gdList', "component" = 'gongzheng/gongzheng/gd/list', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'gz:gd:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 13:53:30', "update_by" = 1, "update_time" = '2025-08-13 15:21:30.886', "remark" = '' WHERE "menu_id" = 1936301429178765314;

UPDATE "public"."sys_menu" SET "menu_name" = '发证', "parent_id" = 1930828700166221826, "order_num" = 9, "path" = 'fzList', "component" = 'gongzheng/gongzheng/fz/list', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'gz:fz:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 14:05:41', "update_by" = 1, "update_time" = '2025-08-13 15:21:18.339', "remark" = '' WHERE "menu_id" = 1936304496540389378;

UPDATE "public"."sys_menu" SET "menu_name" = '制证', "parent_id" = 1930828700166221826, "order_num" = 8, "path" = 'zzList', "component" = 'gongzheng/gongzheng/zz/list', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'gz:zz:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 14:06:32', "update_by" = 1, "update_time" = '2025-08-13 15:21:02.212', "remark" = '' WHERE "menu_id" = 1936304709514563586;

UPDATE "public"."sys_menu" SET "menu_name" = '审批', "parent_id" = 1930828700166221826, "order_num" = 2, "path" = 'spList', "component" = 'gongzheng/gongzheng/sp/list', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'gz:sp:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 14:17:28', "update_by" = 1, "update_time" = '2025-08-13 15:20:44.781', "remark" = '' WHERE "menu_id" = 1936307462697652225;

UPDATE "public"."sys_menu" SET "menu_name" = '上架', "parent_id" = 1930828700166221826, "order_num" = 11, "path" = 'sjList', "component" = 'gongzheng/gongzheng/sj/list', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'gz:sj:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 14:19:39', "update_by" = 1, "update_time" = '2025-08-13 15:21:42.377', "remark" = '' WHERE "menu_id" = 1936308011698491394;

UPDATE "public"."sys_menu" SET "menu_name" = '待校对翻译', "parent_id" = 1930828931645665281, "order_num" = 1, "path" = 'djdfy', "component" = 'gongzheng/fy/djdfy', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'fygl:djd:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:25:12', "update_by" = 1, "update_time" = '2025-08-13 15:17:30.938', "remark" = '' WHERE "menu_id" = 1936430205162225666;

UPDATE "public"."sys_menu" SET "menu_name" = '我的翻译', "parent_id" = 1930828931645665281, "order_num" = 2, "path" = 'wdfy', "component" = 'gongzheng/fy/wdfy', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'fygl:my:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:25:35', "update_by" = 1, "update_time" = '2025-08-13 15:17:48.472', "remark" = '' WHERE "menu_id" = 1936430301148872705;

UPDATE "public"."sys_menu" SET "menu_name" = '已翻译列表', "parent_id" = 1930828931645665281, "order_num" = 3, "path" = 'yfylb', "component" = 'gongzheng/fy/yfylb', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'fygl:yfy:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:26:02', "update_by" = 1, "update_time" = '2025-08-13 15:18:05.976', "remark" = '' WHERE "menu_id" = 1936430412776079361;

UPDATE "public"."sys_menu" SET "menu_name" = '特殊流程发起', "parent_id" = 1930829335016075266, "order_num" = 1, "path" = 'tslcFq', "component" = 'gongzheng/tslc/tslc_fq', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'tslc:fq:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:35:31', "update_by" = 1, "update_time" = '2025-08-13 15:18:25.471', "remark" = '' WHERE "menu_id" = 1936432800865976321;

UPDATE "public"."sys_menu" SET "menu_name" = '特殊流程审批', "parent_id" = 1930829335016075266, "order_num" = 2, "path" = 'tslcSp', "component" = 'gongzheng/tslc/tslc_sp', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'tslc:fq:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:35:58', "update_by" = 1, "update_time" = '2025-08-13 15:18:48.148', "remark" = '' WHERE "menu_id" = 1936432914552586242;

UPDATE "public"."sys_menu" SET "menu_name" = '办证辅助', "parent_id" = 0, "order_num" = 3, "path" = 'bzfz', "component" = NULL, "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'M', "visible" = '0', "status" = '0', "perms" = NULL, "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:40:18', "update_by" = 1, "update_time" = '2025-08-16 14:14:40.804', "remark" = '' WHERE "menu_id" = 1936434006321528833;

UPDATE "public"."sys_menu" SET "menu_name" = '咨询', "parent_id" = 1936434006321528833, "order_num" = 1, "path" = 'zxList', "component" = 'gongzheng/bzfz/zx/index', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'bzfz:zx:query', "icon" = 'list', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-21 22:40:55', "update_by" = 1, "update_time" = '2025-08-16 14:18:05.052', "remark" = '' WHERE "menu_id" = 1936434159682060289;

UPDATE "public"."sys_menu" SET "menu_name" = '编辑', "parent_id" = 1936434159682060289, "order_num" = 1, "path" = '', "component" = NULL, "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'F', "visible" = '0', "status" = '0', "perms" = 'bzfz:zx:edit', "icon" = '', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 16:26:00', "update_by" = 1, "update_time" = '2025-08-13 15:15:47.759', "remark" = '' WHERE "menu_id" = 1954459203355721729;

UPDATE "public"."sys_menu" SET "menu_name" = '提存款申请', "parent_id" = 1936434006321528833, "order_num" = 20, "path" = 'bzfz/tcsq', "component" = 'bzfz/tcsq/index', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'bzfz:tcsq:query', "icon" = 'tab', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 16:28:08', "update_by" = 1, "update_time" = '2025-08-20 17:03:58.815', "remark" = '' WHERE "menu_id" = 1954459737206734849;

UPDATE "public"."sys_menu" SET "menu_name" = '提存款审批', "parent_id" = 1936434006321528833, "order_num" = 30, "path" = 'bzfz/tcsp', "component" = 'bzfz/tcsp/index', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'bzfz:txsp:query', "icon" = 'tab', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 16:29:29', "update_by" = 1, "update_time" = '2025-08-20 17:04:10.011', "remark" = '' WHERE "menu_id" = 1954460080208527361;

UPDATE "public"."sys_menu" SET "menu_name" = '编辑', "parent_id" = 1954459737206734849, "order_num" = 1, "path" = '', "component" = NULL, "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'F', "visible" = '0', "status" = '0', "perms" = 'bzfz:tcsq:edit', "icon" = '', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 16:30:20', "update_by" = 1, "update_time" = '2025-08-13 15:16:56.075', "remark" = '' WHERE "menu_id" = 1954460291756638209;

UPDATE "public"."sys_menu" SET "menu_name" = '编辑', "parent_id" = 1936432914552586242, "order_num" = 1, "path" = '', "component" = NULL, "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'F', "visible" = '0', "status" = '0', "perms" = 'tslc:fq:edit', "icon" = '', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 18:13:12', "update_by" = 1, "update_time" = '2025-08-13 21:18:11.927', "remark" = '' WHERE "menu_id" = 1954486181244878849;

UPDATE "public"."sys_role" SET "tenant_id" = '000000', "role_name" = '翻译主管', "role_key" = 'fyzg', "role_sort" = 41, "data_scope" = '1', "menu_check_strictly" = 't', "dept_check_strictly" = 't', "status" = '0', "del_flag" = '0', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 19:13:41', "update_by" = 1, "update_time" = '2025-08-20 11:38:02.507', "remark" = '' WHERE "role_id" = 1954501398976671746;

UPDATE "public"."sys_role" SET "tenant_id" = '000000', "role_name" = '系统管理员', "role_key" = 'sysadmin', "role_sort" = 99, "data_scope" = '1', "menu_check_strictly" = 't', "dept_check_strictly" = 't', "status" = '0', "del_flag" = '0', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 19:16:39', "update_by" = 1, "update_time" = '2025-08-20 11:37:32.141', "remark" = '' WHERE "role_id" = 1954502146447781889;

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954501398976671746, 1936430301148872705);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954501398976671746, 1954485693715759105);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954501398976671746, 1954485761407631362);

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 100, "user_name" = 'admin', "nick_name" = '超级管理员', "user_type" = 'sys_user', "email" = '<EMAIL>', "phonenumber" = '15888888888', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', "status" = '0', "del_flag" = '0', "login_ip" = '*************', "login_date" = '2025-08-20 11:21:22.741', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-02 12:32:36.692395', "update_by" = 1, "update_time" = '2025-08-20 11:21:22.748', "remark" = '管理员', "english_name" = NULL, "practice_no" = NULL, "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = NULL, "query_db_ids" = NULL, "real_name" = '超级管理员' WHERE "user_id" = 1;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 102, "user_name" = 'gzy1', "nick_name" = '公证员1', "user_type" = 'sys_user', "email" = '<EMAIL>', "phonenumber" = '15177198241', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$u3HqtwSFCluvx7.UpnlT2u9bNT4IQAd7O0ebk1SzUzT.JW7lqwI3C', "status" = '0', "del_flag" = '0', "login_ip" = '0:0:0:0:0:0:0:1', "login_date" = '2025-08-13 23:43:57.888', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-19 09:48:44', "update_by" = 1935515056178139138, "update_time" = '2025-08-13 23:43:57.889', "remark" = '', "english_name" = 'test', "practice_no" = '123', "id_card_no" = '320924199103262114', "signature_oss_id" = 1954887103511343106, "notary_office_id" = 100, "query_db_ids" = '102', "real_name" = '公证员1' WHERE "user_id" = 1935515056178139138;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 101, "user_name" = 'gzyzl1', "nick_name" = '公证员助理1', "user_type" = 'sys_user', "email" = '', "phonenumber" = '13477778888', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$zf7uITTKeMBX1Rg5hvOgW.pogfL81viLwYlj.moh/k/EA.46.Yyvu', "status" = '0', "del_flag" = '0', "login_ip" = '', "login_date" = NULL, "create_dept" = 103, "create_by" = 1, "create_time" = '2025-07-03 18:23:51', "update_by" = 1954948962406567938, "update_time" = '2025-08-14 23:22:30.225', "remark" = '', "english_name" = NULL, "practice_no" = '123456', "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = 100, "query_db_ids" = '', "real_name" = '公证员助理1' WHERE "user_id" = 1940718123030896642;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 102, "user_name" = 'spy1', "nick_name" = '审批员1', "user_type" = 'sys_user', "email" = '', "phonenumber" = '', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$FDW8eS53snnfyndjvWvp4uBdsG62wvQp38arptF2WlHG2C2yB3nva', "status" = '0', "del_flag" = '0', "login_ip" = '0:0:0:0:0:0:0:1', "login_date" = '2025-08-10 22:09:05.609', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-07-03 18:24:29', "update_by" = 1954948962406567938, "update_time" = '2025-08-14 23:22:47.649', "remark" = '', "english_name" = NULL, "practice_no" = '1234567', "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = 100, "query_db_ids" = '', "real_name" = '审批员1' WHERE "user_id" = 1940718281005162497;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 102, "user_name" = 'gzy2', "nick_name" = '公证员2', "user_type" = 'sys_user', "email" = '', "phonenumber" = '13412341234', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$guRC.9mZKGYNV7M0H42xsOfv8yWOB6meTJalnQkEnBrp3mOXE7u96', "status" = '0', "del_flag" = '0', "login_ip" = '0:0:0:0:0:0:0:1', "login_date" = '2025-08-10 22:08:42.407', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-07-06 00:25:07', "update_by" = 1954948962406567938, "update_time" = '2025-08-14 23:22:58.347', "remark" = '', "english_name" = NULL, "practice_no" = '123456', "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = 100, "query_db_ids" = '', "real_name" = '公证员2' WHERE "user_id" = 1941533812272566274;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 102, "user_name" = 'fzy1', "nick_name" = '发证员1', "user_type" = 'sys_user', "email" = '', "phonenumber" = '', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$TsMaqF4odHiYwxQktspCJ.MlHHEsYcnOOemi05tCCzW/j8vo1Eq2K', "status" = '0', "del_flag" = '0', "login_ip" = '', "login_date" = NULL, "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 18:51:10', "update_by" = 1, "update_time" = '2025-08-12 00:13:05.292', "remark" = '', "english_name" = NULL, "practice_no" = NULL, "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = NULL, "query_db_ids" = '', "real_name" = '发证员1' WHERE "user_id" = 1954495732648128514;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 101, "user_name" = 'tsjzspy', "nick_name" = '特殊卷宗审批员1', "user_type" = 'sys_user', "email" = '', "phonenumber" = '', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$vXDEY0hOPtGg10KBt50ywumYn3CA5ksu7kTEAbl2rRyZAziPOBKCy', "status" = '0', "del_flag" = '0', "login_ip" = '0:0:0:0:0:0:0:1', "login_date" = '2025-08-13 21:22:23.885', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 20:53:49', "update_by" = 1954526601446305793, "update_time" = '2025-08-13 21:22:23.886', "remark" = '', "english_name" = NULL, "practice_no" = NULL, "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = NULL, "query_db_ids" = '', "real_name" = '特殊卷宗审批员1' WHERE "user_id" = 1954526601446305793;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 101, "user_name" = 'cwy1', "nick_name" = '财务员1', "user_type" = 'sys_user', "email" = '', "phonenumber" = '', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$uHYc3bA5rXQGVcnOFaUJROzPc/n14aKmvMuCZC/z6fYqFMLl.Joyy', "status" = '0', "del_flag" = '0', "login_ip" = '0:0:0:0:0:0:0:1', "login_date" = '2025-08-10 22:09:35.156', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 20:54:15', "update_by" = 1, "update_time" = '2025-08-12 00:13:38.999', "remark" = '', "english_name" = NULL, "practice_no" = NULL, "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = NULL, "query_db_ids" = '', "real_name" = '财务员1' WHERE "user_id" = 1954526708837265409;

UPDATE "public"."sys_user" SET "tenant_id" = '000000', "dept_id" = 100, "user_name" = 'sysadmin', "nick_name" = '系统管理员', "user_type" = 'sys_user', "email" = '', "phonenumber" = '', "sex" = '0', "avatar" = NULL, "password" = '$2a$10$A2OODi3ucB4Li4ula9dPtOyFi9emtSYS7SQJDwAhQMCCDbrFT.AFK', "status" = '0', "del_flag" = '0', "login_ip" = '*************', "login_date" = '2025-08-12 00:54:21.977', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-12 00:52:08', "update_by" = 1954948962406567938, "update_time" = '2025-08-12 00:54:21.977', "remark" = '', "english_name" = 'sysadmin', "practice_no" = '123456', "id_card_no" = NULL, "signature_oss_id" = NULL, "notary_office_id" = 100, "query_db_ids" = '100,101,102,1946450948568776705,1946451023277719553', "real_name" = '系统管理员' WHERE "user_id" = 1954948962406567938;
