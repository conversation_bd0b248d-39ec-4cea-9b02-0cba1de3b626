package com.gxgz.basicdata.controller;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import com.gxgz.basicdata.domain.GzGzsxJspz;
import com.gxgz.basicdata.domain.bo.*;
import com.gxgz.basicdata.domain.vo.*;
import com.gxgz.basicdata.service.*;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 公证事项配置
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxPz")
public class GzGzsxPzController extends BaseController {

    private final IGzGzsxPzService gzGzsxPzService;

    /**
     * 查询公证事项基础配置列表
     */
//    @SaCheckPermission("basicdata:gzsxPz:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxPzVo> list(GzGzsxPzBo bo, PageQuery pageQuery) {
        return gzGzsxPzService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出公证事项基础配置列表
     */
    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:export")
    @Log(title = "公证事项基础配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxPzBo bo, HttpServletResponse response) {
        List<GzGzsxPzVo> list = gzGzsxPzService.queryList(bo);
        ExcelUtil.exportExcel(list, "公证事项基础配置", GzGzsxPzVo.class, response);
    }

    /**
     * 获取公证事项基础配置详细信息
     *
     * @param id 主键
     */
    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:query")
    @GetMapping("/{id}")
    public R<GzGzsxPzVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxPzService.queryById(id));
    }

    /**
     * 获取公证事项基础配置详细信息
     *
     * @param gzsxCode 主键
     * @param gzlbValue 主键
     */
//    @SaCheckPermission("basicdata:gzsxPz:query")
    @GetMapping("/by/{gzsxCode}/{gzlbValue}")
    public R<GzGzsxPzVo> getInfo(@NotNull(message = "公证事项不能为空") @PathVariable(value = "gzsxCode") String gzsxCode,
                                 @NotNull(message = "公证类别不能为空") @PathVariable(value = "gzlbValue") String gzlbValue) {
        return R.ok(gzGzsxPzService.queryByCode(gzsxCode, gzlbValue));
    }


    /**
     * 新增或修改公证事项基础配置
     */
//    @SaCheckPermission("basicdata:gzsxPz:add")
    @Log(title = "公证事项基础配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/save")
    public R<GzGzsxPzVo> save(@Validated(AddGroup.class) @RequestBody GzGzsxPzBo bo) {
        Boolean flag = false;
        if(bo.getId() != null){
            flag = gzGzsxPzService.updateByBo(bo);
        }else{
            flag = gzGzsxPzService.insertByBo(bo);
        }
        if(flag){
            GzGzsxPzVo vo = BeanUtil.copyProperties(bo, GzGzsxPzVo.class);
            return R.ok(vo);
        }
        return R.fail("保存失败");
    }
    /**
     * 新增公证事项基础配置
     */
    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:add")
    @Log(title = "公证事项基础配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxPzBo bo) {
        return toAjax(gzGzsxPzService.insertByBo(bo));
    }

    /**
     * 修改公证事项基础配置
     */
    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:edit")
    @Log(title = "公证事项基础配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxPzBo bo) {
        return toAjax(gzGzsxPzService.updateByBo(bo));
    }

    /**
     * 删除公证事项基础配置
     *
     * @param ids 主键串
     */
    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:remove")
    @Log(title = "公证事项基础配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxPzService.deleteWithValidByIds(List.of(ids), true));
    }

    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:list")
    @GetMapping("/listTree")
    public R listTree(GzGzsxPzBo bo) {
        List<GzGzsxPzVo> gzGzsxPzVoList = gzGzsxPzService.listTree(bo);
        return R.ok("ok", gzGzsxPzVoList);
    }

    @Hidden
//    @SaCheckPermission("basicdata:gzsxPz:list")
    @GetMapping("/list/exclude/{gzsId}")
    public R listExclude(@PathVariable(value = "id", required = false) Long id) {
        List<GzGzsxPzVo> gzGzsxPzVoList = gzGzsxPzService.listExclude(id);
        return R.ok("ok", gzGzsxPzVoList);
    }



}
