package com.gxgz.basicdata.controller;

import java.util.List;

import com.gxgz.basicdata.domain.bo.GzGzsxLbpzBo;
import com.gxgz.basicdata.domain.bo.GzGzsxPzBo;
import com.gxgz.basicdata.domain.vo.GzGzsxLbpzVo;
import com.gxgz.basicdata.domain.vo.GzGzsxPzVo;
import com.gxgz.basicdata.domain.vo.GzGzsxVo;
import com.gxgz.basicdata.service.IGzGzsxLbpzService;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.basicdata.domain.vo.GzGzlbpzVo;
import com.gxgz.basicdata.domain.bo.GzGzlbpzBo;
import com.gxgz.basicdata.service.IGzGzlbpzService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 公证类别配置
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzlbpz")
public class GzGzlbpzController extends BaseController {

    private final IGzGzsxLbpzService gzGzsxLbpzService;

    /**
     * 查询公证类别配置列表
     */
//    @SaCheckPermission("basicdata:gzlbpz:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxLbpzVo> list(GzGzsxLbpzBo bo) {
        List<GzGzsxLbpzVo> list = gzGzsxLbpzService.queryList(bo);
        return TableDataInfo.build(list);
    }

    /**
     * 修改公证类别配置
     */
//    @SaCheckPermission("basicdata:gzlbpz:edit")
    @Log(title = "公证类别配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxLbpzBo bo) {
        return toAjax(gzGzsxLbpzService.updateByBo(bo));
    }

    /**
     * 查询公证类别配置列表
     */
//    @SaCheckPermission("basicdata:gzlbpz:list")
    @GetMapping("/tree")
    public TableDataInfo<GzGzsxLbpzVo> tree(GzGzsxLbpzBo bo) {
        List<GzGzsxLbpzVo> list = gzGzsxLbpzService.queryTree(bo);
        return TableDataInfo.build(list);
    }
}
