package com.gxgz.guichain.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gxgz.common.mybatis.core.domain.BaseEntity;
import com.gxgz.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 桂链存证信息对象 gxbass_chain
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gz_gxbass_chain")
public class GxbassChain extends TenantEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 文件主键
     */
    private Long ossId;
    /**
     * 交易哈希
     */
    private String txHash;
    /**
     * 区块高度
     */
    private Long blockHeight;
    /**
     * 区块哈希
     */
    private String blockHash;
    /**
     * 签名方
     */
    private String signer;
    /**
     * 业务ID
     */
    private String bizId;
    /**
     * 交易时间
     */
    private String txTime;
    /**
     * 交易内容
     */
    private String data;
    /**
     * 数字签名
     */
    private String signature;
    /**
     * 用户公钥
     */
    private String publicKey;
    /**
     * 加密方式
     */
    private Long encryptMode;
    /**
     * 存证类型1.数据存证 2.文件存证
     */
    private Long proofType;
    /**
     * 文件存证对应的文件名称，数据存证为空
     */
    private String fileName;
    /**
     * 虚拟通道ID，非虚拟通道加密方式返回空
     */
    private String channelId;
    /**
     * 文件上链IPFS哈希
     */
    private String ipfsPath;
    /**
     * 账户地址
     */
    private String fromAddr;


}
