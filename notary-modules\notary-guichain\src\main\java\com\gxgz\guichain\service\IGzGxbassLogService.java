package com.gxgz.guichain.service;

import com.gxgz.guichain.domain.vo.GzGxbassLogVo;
import com.gxgz.guichain.domain.bo.GzGxbassLogBo;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 公证-桂链-上链日志Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IGzGxbassLogService {

    /**
     * 查询公证-桂链-上链日志
     *
     * @param id 主键
     * @return 公证-桂链-上链日志
     */
    GzGxbassLogVo queryById(Long id);

    /**
     * 分页查询公证-桂链-上链日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 公证-桂链-上链日志分页列表
     */
    TableDataInfo<GzGxbassLogVo> queryPageList(GzGxbassLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的公证-桂链-上链日志列表
     *
     * @param bo 查询条件
     * @return 公证-桂链-上链日志列表
     */
    List<GzGxbassLogVo> queryList(GzGxbassLogBo bo);

    /**
     * 新增公证-桂链-上链日志
     *
     * @param bo 公证-桂链-上链日志
     * @return 是否新增成功
     */
    Boolean insertByBo(GzGxbassLogBo bo);

    /**
     * 修改公证-桂链-上链日志
     *
     * @param bo 公证-桂链-上链日志
     * @return 是否修改成功
     */
    Boolean updateByBo(GzGxbassLogBo bo);

    /**
     * 校验并批量删除公证-桂链-上链日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
