-- ----------------------
-- 数据库表结构更新 2025-09-01
-- ----------------------
CREATE SEQUENCE "public"."gz_gzb_upload_log_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

CREATE SEQUENCE "public"."gz_gzsx_zxbl_config_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

CREATE SEQUENCE "public"."gz_gzsx_zxbl_material_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

ALTER TABLE "public"."gz_gzjz_jbxx" ADD COLUMN "is_gzb_upload" int2 DEFAULT 0;

COMMENT ON COLUMN "public"."gz_gzjz_jbxx"."is_gzb_upload" IS '是否上传 公证簿 0 未上传 1已上传';

ALTER TABLE "public"."gz_gzjz_wjccxx" ADD COLUMN "wb_pdf" text COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."gz_gzjz_wjccxx"."wb_pdf" IS '文本pdf路径，json字符串存储，结构{ossId=OSSID，fileName=文件名，path=保存路径}；';

ALTER TABLE "public"."gz_gzjz_wjccxx" ADD COLUMN "yw_pdf" text COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."gz_gzjz_wjccxx"."yw_pdf" IS '译文pdf路径，json字符串存储，结构{ossId=OSSID，fileName=文件名，path=保存路径}；';

ALTER TABLE "public"."gz_gzjz_wjccxx" ADD COLUMN "wb_pdf_prev" text COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."gz_gzjz_wjccxx"."wb_pdf_prev" IS '前一个版本的文本pdf路径，json字符串存储，结构{ossId=OSSID，fileName=文件名，path=保存路径}；';

ALTER TABLE "public"."gz_gzjz_wjccxx" ADD COLUMN "yw_pdf_prev" text COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."gz_gzjz_wjccxx"."yw_pdf_prev" IS '前一个版本的译文pdf路径，json字符串存储，结构{ossId=OSSID，fileName=文件名，path=保存路径}；';

CREATE TABLE "public"."gz_gzsx_zxbl_config" (
                                                "id" int8 NOT NULL DEFAULT nextval('gz_gzsx_zxbl_config_id_seq'::regclass),
                                                "gzsx_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                "gzsx_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                "gzsx_id" int8 NOT NULL,
                                                "is_online_handle" int2 NOT NULL DEFAULT 0,
                                                "is_enable_handle" int2 NOT NULL DEFAULT 0,
                                                "material_ids" text COLLATE "pg_catalog"."default",
                                                "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                "create_dept" int8,
                                                "create_by" int8,
                                                "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                "update_by" int8,
                                                "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                "remark" varchar(500) COLLATE "pg_catalog"."default",
                                                "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                                CONSTRAINT "gz_gzsx_zxbl_config_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_gzsx_zxbl_config" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."gzsx_name" IS '公证事项名称（冗余字段）';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."gzsx_code" IS '事项编号（冗余字段）';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."gzsx_id" IS '公证事项ID（基于GzsxVO中id）';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."is_online_handle" IS '是否在线办理（1是/0否）';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."is_enable_handle" IS '是否启用办理（1是/0否）';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."material_ids" IS '申办材料ID（多个以逗号分隔）';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."tenant_id" IS '租户ID';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."create_dept" IS '创建部门';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."create_by" IS '创建人';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."update_by" IS '更新人';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."remark" IS '备注';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_config"."del_flag" IS '删除标识：0-正常，1-删除';

COMMENT ON TABLE "public"."gz_gzsx_zxbl_config" IS '在线办理配置表';

CREATE TABLE "public"."gz_gzsx_zxbl_material" (
                                                  "id" int8 NOT NULL DEFAULT nextval('gz_gzsx_zxbl_material_id_seq'::regclass),
                                                  "material_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "category" int2 NOT NULL DEFAULT 1,
                                                  "description" text COLLATE "pg_catalog"."default",
                                                  "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                  "create_dept" int8,
                                                  "create_by" int8,
                                                  "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                  "update_by" int8,
                                                  "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                  "remark" varchar(500) COLLATE "pg_catalog"."default",
                                                  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                                  CONSTRAINT "gz_gzsx_zxbl_material_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_gzsx_zxbl_material" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."material_name" IS '材料名称';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."category" IS '类别：1-个人，2-企业';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."description" IS '说明';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."tenant_id" IS '租户ID';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."create_dept" IS '创建部门';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."create_by" IS '创建人';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."update_by" IS '更新人';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."remark" IS '备注';

COMMENT ON COLUMN "public"."gz_gzsx_zxbl_material"."del_flag" IS '删除标识：0-正常，1-删除';

COMMENT ON TABLE "public"."gz_gzsx_zxbl_material" IS '在线办理材料管理表';

CREATE TABLE "public"."gz_notary_agent_person" (
                                                   "id" int8 NOT NULL,
                                                   "register_id" int8 NOT NULL,
                                                   "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                   "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                   "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                   CONSTRAINT "gz_notary_agent_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_agent_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_agent_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."nature_type" IS '代理人类别';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."person_name" IS '代理人姓名/单位名称';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_agent_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_agent_person" IS '公证登记簿代理人信息表';

CREATE TABLE "public"."gz_notary_client_person" (
                                                    "id" int8 NOT NULL,
                                                    "register_id" int8 NOT NULL,
                                                    "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                    "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                    "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                    CONSTRAINT "gz_notary_client_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_client_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_client_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_client_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_client_person"."nature_type" IS '当事人类别';

COMMENT ON COLUMN "public"."gz_notary_client_person"."person_name" IS '当事人姓名/单位名称';

COMMENT ON COLUMN "public"."gz_notary_client_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_client_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_client_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_client_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_client_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_client_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_client_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_client_person" IS '公证登记簿当事人信息表';

CREATE TABLE "public"."gz_notary_contract_info" (
                                                    "id" int8 NOT NULL,
                                                    "register_id" int8 NOT NULL,
                                                    "borrow_person" varchar(10) COLLATE "pg_catalog"."default",
                                                    "warrant_type" varchar(50) COLLATE "pg_catalog"."default",
                                                    "contract_type" varchar(10) COLLATE "pg_catalog"."default",
                                                    "contract_money" numeric(15,2),
                                                    "convert_rmb_num" numeric(15,2),
                                                    "contract_money_rate_type" varchar(10) COLLATE "pg_catalog"."default",
                                                    "contract_money_rate" varchar(50) COLLATE "pg_catalog"."default",
                                                    "contract_accept_date_start" int8,
                                                    "contract_accept_date_end" int8,
                                                    "contract_accept_date_day_num" varchar(20) COLLATE "pg_catalog"."default",
                                                    "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                    "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                    "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                    CONSTRAINT "gz_notary_contract_info_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_contract_info" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_contract_info"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."borrow_person" IS '出借人';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."warrant_type" IS '担保方式（可多选，用中文顿号分隔）';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_type" IS '合同金额币种';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_money" IS '合同（协议）金额';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."convert_rmb_num" IS '合同金额币种折合人民币金额';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_money_rate_type" IS '借款合同利率类别（1:固定利率;2:浮动利率）';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_money_rate" IS '借款合同利率';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_accept_date_start" IS '借款期限起（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_accept_date_end" IS '借款期限止（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."contract_accept_date_day_num" IS '借款期限天数';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_contract_info"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_contract_info" IS '赋强公证-基本信息表';

CREATE TABLE "public"."gz_notary_contract_person" (
                                                      "id" int8 NOT NULL,
                                                      "register_id" int8 NOT NULL,
                                                      "person_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                      "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                      "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                      "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                      "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                      CONSTRAINT "gz_notary_contract_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_contract_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_contract_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."person_type" IS '人员类型 (debt:债务人;client:债权人;agent:代理人)';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."nature_type" IS '自然人类别';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."person_name" IS '人员名称';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_contract_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_contract_person" IS '赋强公证-当事人信息表';

CREATE TABLE "public"."gz_notary_deposit_info" (
                                                   "id" int8 NOT NULL,
                                                   "register_id" int8 NOT NULL,
                                                   "deposit_date" int8,
                                                   "deposit_due_date" int8,
                                                   "deposit_type" varchar(10) COLLATE "pg_catalog"."default",
                                                   "deposit_money" numeric(15,2),
                                                   "deposit_object_type" varchar(10) COLLATE "pg_catalog"."default",
                                                   "deposit_object_desc" varchar(500) COLLATE "pg_catalog"."default",
                                                   "clearance_date" int8,
                                                   "clearance_type" varchar(10) COLLATE "pg_catalog"."default",
                                                   "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                   "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                   "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                   CONSTRAINT "gz_notary_deposit_info_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_deposit_info" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."deposit_date" IS '提存日期（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."deposit_due_date" IS '提存到期日（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."deposit_type" IS '提存类型';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."deposit_money" IS '提存标的金额';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."deposit_object_type" IS '提存标的类型';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."deposit_object_desc" IS '提存标的描述';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."clearance_date" IS '清偿日期（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."clearance_type" IS '清偿方式';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_deposit_info"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_deposit_info" IS '公证提存-基本信息表';

CREATE TABLE "public"."gz_notary_deposit_person" (
                                                     "id" int8 NOT NULL,
                                                     "register_id" int8 NOT NULL,
                                                     "person_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                     "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                     "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                     "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                     "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                     CONSTRAINT "gz_notary_deposit_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_deposit_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."person_type" IS '人员类型 (depositor:提存人;beneficiary:受益人;agent:代理人)';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."nature_type" IS '自然人类别';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."person_name" IS '人员名称';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_deposit_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_deposit_person" IS '公证提存-当事人信息表';

CREATE TABLE "public"."gz_notary_execute_info" (
                                                   "id" int8 NOT NULL,
                                                   "register_id" int8 NOT NULL,
                                                   "force_notary_num_year" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "force_notary_num_subfix" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "force_notary_num" varchar(100) COLLATE "pg_catalog"."default",
                                                   "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                   "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                   "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                   CONSTRAINT "gz_notary_execute_info_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_execute_info" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_execute_info"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."force_notary_num_year" IS '赋强公证书年度编号';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."force_notary_num_subfix" IS '赋强公证书编号后缀';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."force_notary_num" IS '赋强公证书编号';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_execute_info"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_execute_info" IS '执行证书-基本信息表';

CREATE TABLE "public"."gz_notary_execute_person" (
                                                     "id" int8 NOT NULL,
                                                     "register_id" int8 NOT NULL,
                                                     "person_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'agent'::character varying,
                                                     "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                     "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                     "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                     "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                     "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                     CONSTRAINT "gz_notary_execute_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_execute_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_execute_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."person_type" IS '人员类型（agent:代理人）';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."nature_type" IS '自然人类别';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."person_name" IS '人员名称';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_execute_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_execute_person" IS '执行证书-代理人信息表';

CREATE TABLE "public"."gz_notary_mortgage_info" (
                                                    "id" int8 NOT NULL,
                                                    "register_id" int8 NOT NULL,
                                                    "mortgage_info_date_start" int8,
                                                    "mortgage_info_date_end" int8,
                                                    "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                    "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                    "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                    CONSTRAINT "gz_notary_mortgage_info_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_mortgage_info" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."mortgage_info_date_start" IS '抵押履行债务的期限起（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."mortgage_info_date_end" IS '抵押履行债务的期限止（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_mortgage_info"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_mortgage_info" IS '公证抵押-基本信息表';

CREATE TABLE "public"."gz_notary_mortgage_person" (
                                                      "id" int8 NOT NULL,
                                                      "register_id" int8 NOT NULL,
                                                      "person_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                      "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                      "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                      "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                      "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                      CONSTRAINT "gz_notary_mortgage_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_mortgage_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."person_type" IS '人员类型 (debt:抵押权人;client:抵押人;agent:代理人)';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."nature_type" IS '自然人类别';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."person_name" IS '人员名称';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_mortgage_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_mortgage_person" IS '公证抵押-当事人信息表';

CREATE TABLE "public"."gz_notary_pawn_info" (
                                                "id" int8 NOT NULL,
                                                "register_id" int8 NOT NULL,
                                                "pawn_name" varchar(200) COLLATE "pg_catalog"."default",
                                                "pawn_num" varchar(100) COLLATE "pg_catalog"."default",
                                                "pawn_remark" varchar(500) COLLATE "pg_catalog"."default",
                                                "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                CONSTRAINT "gz_notary_pawn_info_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_pawn_info" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."pawn_name" IS '抵押物名称';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."pawn_num" IS '数量单位';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."pawn_remark" IS '备注';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_pawn_info"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_pawn_info" IS '公证抵押-抵押物信息表';

CREATE TABLE "public"."gz_notary_register" (
                                               "id" int8 NOT NULL,
                                               "register_file_id" varchar(64) COLLATE "pg_catalog"."default",
                                               "zyzbh" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
                                               "notary_office_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
                                               "jgmc" varchar(200) COLLATE "pg_catalog"."default",
                                               "person_base_id" varchar(64) COLLATE "pg_catalog"."default",
                                               "notary_person_name" varchar(100) COLLATE "pg_catalog"."default",
                                               "accept_date" int8 NOT NULL,
                                               "item_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                               "item_sub_type" varchar(50) COLLATE "pg_catalog"."default",
                                               "is_add" bool NOT NULL DEFAULT false,
                                               "notarization_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                               "notary_num_year" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                               "notary_num_subfix" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                               "notary_num" varchar(100) COLLATE "pg_catalog"."default",
                                               "add_notary_num_year" varchar(10) COLLATE "pg_catalog"."default",
                                               "add_notary_num_subfix" varchar(50) COLLATE "pg_catalog"."default",
                                               "add_notary_num" varchar(100) COLLATE "pg_catalog"."default",
                                               "bzjd" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                               "is_secret" bool NOT NULL DEFAULT false,
                                               "use_type" varchar(20) COLLATE "pg_catalog"."default",
                                               "welfare_law_service" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::character varying,
                                               "use_place" varchar(10) COLLATE "pg_catalog"."default",
                                               "new_notarization_business" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::character varying,
                                               "protection_content" varchar(10) COLLATE "pg_catalog"."default",
                                               "epidemic_notarization" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::character varying,
                                               "service_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::character varying,
                                               "takeback_date" int8,
                                               "type_common" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                               "end_type" varchar(10) COLLATE "pg_catalog"."default" DEFAULT '1'::character varying,
                                               "end_date" int8,
                                               "audit_person" varchar(100) COLLATE "pg_catalog"."default",
                                               "item_charge" numeric(15,2),
                                               "remit_item_charge" numeric(15,2),
                                               "register_book_status" varchar(10) COLLATE "pg_catalog"."default",
                                               "revoke_date" int8,
                                               "execute_money" numeric(15,2),
                                               "execute_result" varchar(10) COLLATE "pg_catalog"."default",
                                               "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                               "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                               "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                               "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                               "remark" varchar(500) COLLATE "pg_catalog"."default",
                                               "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                               "is_push" int2 NOT NULL,
                                               "gzjz_id" int8 NOT NULL,
                                               CONSTRAINT "gz_notary_register_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_register" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_register"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_register"."register_file_id" IS '登记簿主表ID（外部系统返回）';

COMMENT ON COLUMN "public"."gz_notary_register"."zyzbh" IS '公证员执业证编号';

COMMENT ON COLUMN "public"."gz_notary_register"."notary_office_id" IS '公证处ID';

COMMENT ON COLUMN "public"."gz_notary_register"."jgmc" IS '公证处名称';

COMMENT ON COLUMN "public"."gz_notary_register"."person_base_id" IS '用户ID';

COMMENT ON COLUMN "public"."gz_notary_register"."notary_person_name" IS '用户名';

COMMENT ON COLUMN "public"."gz_notary_register"."accept_date" IS '受理日期（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_register"."item_name" IS '公证业务类别';

COMMENT ON COLUMN "public"."gz_notary_register"."item_sub_type" IS '公证事项关联细项';

COMMENT ON COLUMN "public"."gz_notary_register"."is_add" IS '是否补证';

COMMENT ON COLUMN "public"."gz_notary_register"."notarization_type" IS '公证类型';

COMMENT ON COLUMN "public"."gz_notary_register"."notary_num_year" IS '公证书年份';

COMMENT ON COLUMN "public"."gz_notary_register"."notary_num_subfix" IS '公证书编号后缀';

COMMENT ON COLUMN "public"."gz_notary_register"."notary_num" IS '完整公证书编号';

COMMENT ON COLUMN "public"."gz_notary_register"."add_notary_num_year" IS '补证公证书年份';

COMMENT ON COLUMN "public"."gz_notary_register"."add_notary_num_subfix" IS '补证公证书后缀';

COMMENT ON COLUMN "public"."gz_notary_register"."add_notary_num" IS '补证公证书编号';

COMMENT ON COLUMN "public"."gz_notary_register"."bzjd" IS '办证进度';

COMMENT ON COLUMN "public"."gz_notary_register"."is_secret" IS '是否密卷';

COMMENT ON COLUMN "public"."gz_notary_register"."use_type" IS '用途';

COMMENT ON COLUMN "public"."gz_notary_register"."welfare_law_service" IS '公益法律服务';

COMMENT ON COLUMN "public"."gz_notary_register"."use_place" IS '使用地';

COMMENT ON COLUMN "public"."gz_notary_register"."new_notarization_business" IS '新型公证业务';

COMMENT ON COLUMN "public"."gz_notary_register"."protection_content" IS '保护内容';

COMMENT ON COLUMN "public"."gz_notary_register"."epidemic_notarization" IS '涉疫情/灾情公证';

COMMENT ON COLUMN "public"."gz_notary_register"."service_type" IS '服务类型';

COMMENT ON COLUMN "public"."gz_notary_register"."takeback_date" IS '遗嘱保管取回日期';

COMMENT ON COLUMN "public"."gz_notary_register"."type_common" IS '是否共同遗嘱';

COMMENT ON COLUMN "public"."gz_notary_register"."end_type" IS '结案方式';

COMMENT ON COLUMN "public"."gz_notary_register"."end_date" IS '办结日期/不予办理日期/终止公证日期';

COMMENT ON COLUMN "public"."gz_notary_register"."audit_person" IS '审批人';

COMMENT ON COLUMN "public"."gz_notary_register"."item_charge" IS '实收金额';

COMMENT ON COLUMN "public"."gz_notary_register"."remit_item_charge" IS '减免金额';

COMMENT ON COLUMN "public"."gz_notary_register"."register_book_status" IS '公证书状态';

COMMENT ON COLUMN "public"."gz_notary_register"."revoke_date" IS '撤证日期';

COMMENT ON COLUMN "public"."gz_notary_register"."execute_money" IS '执行金额';

COMMENT ON COLUMN "public"."gz_notary_register"."execute_result" IS '执行结果';

COMMENT ON COLUMN "public"."gz_notary_register"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_register"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_register"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_register"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_register"."remark" IS '备注';

COMMENT ON COLUMN "public"."gz_notary_register"."tenant_id" IS '租户ID';

COMMENT ON COLUMN "public"."gz_notary_register"."is_push" IS '是否推送 0 未推送 1已推送';

COMMENT ON COLUMN "public"."gz_notary_register"."gzjz_id" IS '公证卷宗ID';

COMMENT ON TABLE "public"."gz_notary_register" IS '公证登记簿主表';

CREATE TABLE "public"."gz_notary_will_person" (
                                                  "id" int8 NOT NULL,
                                                  "register_id" int8 NOT NULL,
                                                  "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "birth_date" int8,
                                                  "type_common" varchar(10) COLLATE "pg_catalog"."default",
                                                  "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                  "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                  "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                  "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                  "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                  CONSTRAINT "gz_notary_will_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_will_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_will_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_will_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_will_person"."person_name" IS '遗嘱人姓名';

COMMENT ON COLUMN "public"."gz_notary_will_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_will_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_will_person"."birth_date" IS '出生日期（毫秒时间戳）';

COMMENT ON COLUMN "public"."gz_notary_will_person"."type_common" IS '遗嘱类型（0:自书遗嘱;1:代书遗嘱）';

COMMENT ON COLUMN "public"."gz_notary_will_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_will_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_will_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_will_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_will_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_will_person" IS '遗嘱备案-遗嘱人信息表';

CREATE TABLE "public"."gz_notary_will_register_person" (
                                                           "id" int8 NOT NULL,
                                                           "register_id" int8 NOT NULL,
                                                           "person_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "nature_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "person_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "certificate_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "certificate_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "create_by" varchar(64) COLLATE "pg_catalog"."default",
                                                           "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                           "update_by" varchar(64) COLLATE "pg_catalog"."default",
                                                           "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                           "tenant_id" varchar(20) COLLATE "pg_catalog"."default",
                                                           CONSTRAINT "gz_notary_will_register_person_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_notary_will_register_person" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."id" IS '主键ID';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."register_id" IS '登记簿主表ID';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."person_type" IS '人员类型（agent:代理人, client:领取人等）';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."nature_type" IS '自然人类别';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."person_name" IS '人员名称';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."certificate_type" IS '证件类型';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."certificate_num" IS '证件号码';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."create_by" IS '创建者';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."update_by" IS '更新者';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_notary_will_register_person"."tenant_id" IS '租户ID';

COMMENT ON TABLE "public"."gz_notary_will_register_person" IS '遗嘱保管/备案-人员信息表';

CREATE TABLE "public"."gz_todo_notification" (
                                                 "id" int8 NOT NULL,
                                                 "todo_title" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "todo_content" text COLLATE "pg_catalog"."default",
                                                 "receiver_id" int8 NOT NULL,
                                                 "receiver_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "is_read" int4,
                                                 "read_time" timestamp(6),
                                                 "task_id" varchar(100) COLLATE "pg_catalog"."default",
                                                 "due_date" timestamp(6),
                                                 "ext_data" jsonb,
                                                 "tenant_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "create_dept" int8,
                                                 "create_by" int8 NOT NULL,
                                                 "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 "update_by" int8,
                                                 "update_time" timestamp(6),
                                                 "remark" text COLLATE "pg_catalog"."default",
                                                 "del_flag" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '0'::bpchar,
                                                 CONSTRAINT "gz_todo_notification_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "public"."gz_todo_notification" OWNER TO "postgres";

COMMENT ON COLUMN "public"."gz_todo_notification"."todo_title" IS '待办标题';

COMMENT ON COLUMN "public"."gz_todo_notification"."todo_content" IS '待办内容描述';

COMMENT ON COLUMN "public"."gz_todo_notification"."receiver_id" IS '接收人用户ID';

COMMENT ON COLUMN "public"."gz_todo_notification"."receiver_name" IS '接收人姓名';

COMMENT ON COLUMN "public"."gz_todo_notification"."is_read" IS '是否已读';

COMMENT ON COLUMN "public"."gz_todo_notification"."read_time" IS '读取时间';

COMMENT ON COLUMN "public"."gz_todo_notification"."task_id" IS '任务ID';

COMMENT ON COLUMN "public"."gz_todo_notification"."due_date" IS '截止时间';

COMMENT ON COLUMN "public"."gz_todo_notification"."ext_data" IS '扩展数据（JSON格式）';

COMMENT ON COLUMN "public"."gz_todo_notification"."tenant_id" IS '租户ID';

COMMENT ON COLUMN "public"."gz_todo_notification"."create_dept" IS '创建部门';

COMMENT ON COLUMN "public"."gz_todo_notification"."create_by" IS '创建人';

COMMENT ON COLUMN "public"."gz_todo_notification"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."gz_todo_notification"."update_by" IS '更新人';

COMMENT ON COLUMN "public"."gz_todo_notification"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."gz_todo_notification"."remark" IS '备注';

COMMENT ON COLUMN "public"."gz_todo_notification"."del_flag" IS '删除标识（0正常 1删除）';

COMMENT ON TABLE "public"."gz_todo_notification" IS '待办事项通知主表（站内信）';

ALTER TABLE "public"."sys_tenant" ADD COLUMN "gzb_org_id" varchar(64) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."sys_tenant"."gzb_org_id" IS '公正簿机构ID';

ALTER TABLE "public"."gz_notary_agent_person" ADD CONSTRAINT "fk_gz_agent_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_client_person" ADD CONSTRAINT "fk_gz_client_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_contract_info" ADD CONSTRAINT "fk_gz_contract_info_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_contract_person" ADD CONSTRAINT "fk_gz_contract_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_deposit_info" ADD CONSTRAINT "fk_gz_deposit_info_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_deposit_person" ADD CONSTRAINT "fk_gz_deposit_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_execute_info" ADD CONSTRAINT "fk_gz_execute_info_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_execute_person" ADD CONSTRAINT "fk_gz_execute_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_mortgage_info" ADD CONSTRAINT "fk_gz_mortgage_info_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_mortgage_person" ADD CONSTRAINT "fk_gz_mortgage_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_pawn_info" ADD CONSTRAINT "fk_gz_pawn_info_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_will_person" ADD CONSTRAINT "fk_gz_will_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."gz_notary_will_register_person" ADD CONSTRAINT "fk_gz_will_register_person_register_id" FOREIGN KEY ("register_id") REFERENCES "public"."gz_notary_register" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

SELECT setval('"public"."gz_gzb_upload_log_id_seq"', 1, false);

ALTER SEQUENCE "public"."gz_gzb_upload_log_id_seq" OWNER TO "postgres";

SELECT setval('"public"."gz_gzsx_zxbl_config_id_seq"', 1, false);

ALTER SEQUENCE "public"."gz_gzsx_zxbl_config_id_seq"
    OWNED BY "public"."gz_gzsx_zxbl_config"."id";

ALTER SEQUENCE "public"."gz_gzsx_zxbl_config_id_seq" OWNER TO "postgres";

SELECT setval('"public"."gz_gzsx_zxbl_material_id_seq"', 1, false);

ALTER SEQUENCE "public"."gz_gzsx_zxbl_material_id_seq"
    OWNED BY "public"."gz_gzsx_zxbl_material"."id";

ALTER SEQUENCE "public"."gz_gzsx_zxbl_material_id_seq" OWNER TO "postgres";

CREATE INDEX "idx_gz_agent_person_certificate_num" ON "public"."gz_notary_agent_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_agent_person_nature_type" ON "public"."gz_notary_agent_person" USING btree (
    "nature_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_agent_person_register_id" ON "public"."gz_notary_agent_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_client_person_certificate_num" ON "public"."gz_notary_client_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_client_person_nature_type" ON "public"."gz_notary_client_person" USING btree (
    "nature_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_client_person_register_id" ON "public"."gz_notary_client_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_contract_info_contract_type" ON "public"."gz_notary_contract_info" USING btree (
    "contract_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_contract_info_register_id" ON "public"."gz_notary_contract_info" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_contract_person_certificate_num" ON "public"."gz_notary_contract_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_contract_person_person_type" ON "public"."gz_notary_contract_person" USING btree (
    "person_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_contract_person_register_id" ON "public"."gz_notary_contract_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_deposit_info_deposit_date" ON "public"."gz_notary_deposit_info" USING btree (
    "deposit_date" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_deposit_info_deposit_type" ON "public"."gz_notary_deposit_info" USING btree (
    "deposit_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_deposit_info_register_id" ON "public"."gz_notary_deposit_info" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_deposit_person_certificate_num" ON "public"."gz_notary_deposit_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_deposit_person_person_type" ON "public"."gz_notary_deposit_person" USING btree (
    "person_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_deposit_person_register_id" ON "public"."gz_notary_deposit_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_execute_info_force_notary_num" ON "public"."gz_notary_execute_info" USING btree (
    "force_notary_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_execute_info_register_id" ON "public"."gz_notary_execute_info" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_execute_person_certificate_num" ON "public"."gz_notary_execute_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_execute_person_register_id" ON "public"."gz_notary_execute_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_mortgage_info_register_id" ON "public"."gz_notary_mortgage_info" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_mortgage_person_certificate_num" ON "public"."gz_notary_mortgage_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_mortgage_person_person_type" ON "public"."gz_notary_mortgage_person" USING btree (
    "person_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_mortgage_person_register_id" ON "public"."gz_notary_mortgage_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_notary_register_accept_date" ON "public"."gz_notary_register" USING btree (
    "accept_date" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_notary_register_bzjd" ON "public"."gz_notary_register" USING btree (
    "bzjd" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_notary_register_create_time" ON "public"."gz_notary_register" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_notary_register_item_name" ON "public"."gz_notary_register" USING btree (
    "item_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_notary_register_notary_num" ON "public"."gz_notary_register" USING btree (
    "notary_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_notary_register_office_id" ON "public"."gz_notary_register" USING btree (
    "notary_office_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_pawn_info_register_id" ON "public"."gz_notary_pawn_info" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_will_person_certificate_num" ON "public"."gz_notary_will_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_will_person_register_id" ON "public"."gz_notary_will_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_will_register_person_certificate_num" ON "public"."gz_notary_will_register_person" USING btree (
    "certificate_num" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_will_register_person_person_type" ON "public"."gz_notary_will_register_person" USING btree (
    "person_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_will_register_person_register_id" ON "public"."gz_notary_will_register_person" USING btree (
    "register_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_create_time" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_del_flag" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "del_flag" COLLATE "pg_catalog"."default" "pg_catalog"."bpchar_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_enable_handle" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "is_enable_handle" "pg_catalog"."int2_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_gzsx_code" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "gzsx_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_gzsx_id" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "gzsx_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_online_handle" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "is_online_handle" "pg_catalog"."int2_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_config_tenant" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "tenant_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_material_category" ON "public"."gz_gzsx_zxbl_material" USING btree (
    "category" "pg_catalog"."int2_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_material_create_time" ON "public"."gz_gzsx_zxbl_material" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_material_del_flag" ON "public"."gz_gzsx_zxbl_material" USING btree (
    "del_flag" COLLATE "pg_catalog"."default" "pg_catalog"."bpchar_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_material_name" ON "public"."gz_gzsx_zxbl_material" USING btree (
    "material_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_gz_zxbl_material_tenant" ON "public"."gz_gzsx_zxbl_material" USING btree (
    "tenant_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_todo_notification_create_time" ON "public"."gz_todo_notification" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_todo_notification_is_read" ON "public"."gz_todo_notification" USING btree (
    "is_read" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_todo_notification_receiver_id" ON "public"."gz_todo_notification" USING btree (
    "receiver_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_todo_notification_tenant_id" ON "public"."gz_todo_notification" USING btree (
    "tenant_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE INDEX "idx_todo_notification_unread" ON "public"."gz_todo_notification" USING btree (
    "receiver_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
    "is_read" "pg_catalog"."int4_ops" ASC NULLS LAST,
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

CREATE UNIQUE INDEX "uk_gz_notary_register_file_id" ON "public"."gz_notary_register" USING btree (
    "register_file_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

CREATE UNIQUE INDEX "uk_gz_zxbl_config_gzsx" ON "public"."gz_gzsx_zxbl_config" USING btree (
    "gzsx_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
    "tenant_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    ) WHERE del_flag = '0'::bpchar;


-- ----------------------
-- 数据库数据更新 2025-09-01
-- ----------------------

INSERT INTO "public"."sys_config" ("config_id", "tenant_id", "config_name", "config_key", "config_value", "config_type", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1960260861994147841, '000000', '客户号流水号', 'gz.khh.lsh', '100002', 'N', 100, 1, '2025-08-26 16:39:44.042', 1, '2025-08-26 16:59:08.401', '默认100000开始，避免与旧系统重复');

UPDATE "public"."sys_dict_data" SET "tenant_id" = '000000', "dict_sort" = 10, "dict_label" = '人民币', "dict_value" = '1', "dict_type" = 'gz_tc_bz', "css_class" = '', "list_class" = 'primary', "is_default" = 'N', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-07-02 13:14:09', "update_by" = 1, "update_time" = '2025-09-01 10:59:27.503', "remark" = '' WHERE "dict_code" = 1940277792816656386;

UPDATE "public"."sys_dict_type" SET "tenant_id" = '000000', "dict_name" = '公证-提存-币种', "dict_type" = 'gz_tc_bz', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-07-02 13:13:57', "update_by" = 1, "update_time" = '2025-09-01 10:59:42.486', "remark" = '人民币、美元、欧元、英镑' WHERE "dict_id" = 1940277742162046977;

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1959983955797729282, '文档对比DEMO', 1935520575919951874, 1, 'openWordCompare', 'gongzheng/dev/pageoffice/openWordCompare', NULL, '1', '0', 'C', '0', '0', NULL, 'excel', 100, 1, '2025-08-25 22:19:24.465', 1, '2025-08-25 22:19:24.465', '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055264514363394, '公证登记簿', 0, 98, 'notaryRegister', 'gzb/notaryRegister/index', NULL, '1', '0', 'C', '0', '0', 'gzb:notaryRegister:list', 'guide', 103, 1, '2025-08-28 21:47:59', 1, '2025-08-28 21:49:35.494', '公证登记簿主菜单');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055264514363395, '公证登记簿主查询', 1961055264514363394, 1, '#', '', NULL, '1', '0', 'F', '0', '0', 'gzb:notaryRegister:query', '#', 103, 1, '2025-08-28 21:47:59.687439', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055264514363396, '公证登记簿主新增', 1961055264514363394, 2, '#', '', NULL, '1', '0', 'F', '0', '0', 'gzb:notaryRegister:add', '#', 103, 1, '2025-08-28 21:47:59.72498', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055264514363397, '公证登记簿主修改', 1961055264514363394, 3, '#', '', NULL, '1', '0', 'F', '0', '0', 'gzb:notaryRegister:edit', '#', 103, 1, '2025-08-28 21:47:59.762511', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055264514363398, '公证登记簿主删除', 1961055264514363394, 4, '#', '', NULL, '1', '0', 'F', '0', '0', 'gzb:notaryRegister:remove', '#', 103, 1, '2025-08-28 21:47:59.799979', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055264514363399, '公证登记簿主导出', 1961055264514363394, 5, '#', '', NULL, '1', '0', 'F', '0', '0', 'gzb:notaryRegister:export', '#', 103, 1, '2025-08-28 21:47:59.838918', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055270076010497, '消息提醒', 0, 0, 'todoNotification', 'notification/todoNotification/index', NULL, '1', '0', 'C', '0', '0', 'notification:todoNotification:list', 'email', 103, 1, '2025-08-28 21:25:38', 1, '2025-08-28 21:36:34.379', '待办事项通知主（站内信）菜单');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055270076010498, '待办事项通知主（站内信）查询', 1961055270076010497, 1, '#', '', NULL, '1', '0', 'F', '0', '0', 'notification:todoNotification:query', '#', 103, 1, '2025-08-28 21:25:38.529202', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055270076010499, '待办事项通知主（站内信）新增', 1961055270076010497, 2, '#', '', NULL, '1', '0', 'F', '0', '0', 'notification:todoNotification:add', '#', 103, 1, '2025-08-28 21:25:38.568212', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055270076010500, '待办事项通知主（站内信）修改', 1961055270076010497, 3, '#', '', NULL, '1', '0', 'F', '0', '0', 'notification:todoNotification:edit', '#', 103, 1, '2025-08-28 21:25:38.60871', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055270076010501, '待办事项通知主（站内信）删除', 1961055270076010497, 4, '#', '', NULL, '1', '0', 'F', '0', '0', 'notification:todoNotification:remove', '#', 103, 1, '2025-08-28 21:25:38.648472', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1961055270076010502, '待办事项通知主（站内信）导出', 1961055270076010497, 5, '#', '', NULL, '1', '0', 'F', '0', '0', 'notification:todoNotification:export', '#', 103, 1, '2025-08-28 21:25:38.688269', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029462946648066, '智桂通-在线办理-材料管理', 1929410395467423746, 11, 'blcl', 'gongzheng/basicdata/zxbl/blcl/index', NULL, '1', '0', 'C', '0', '0', 'basicdata:gzsxZxblMaterial:list', 'nested', 103, 1, '2025-08-31 13:56:15', 1, '2025-08-31 14:00:26.923', '在线办理材料管理菜单');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029462946648067, '在线办理材料管理查询', 1962029462946648066, 1, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblMaterial:query', '#', 103, 1, '2025-08-31 13:56:15.41812', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029462946648068, '在线办理材料管理新增', 1962029462946648066, 2, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblMaterial:add', '#', 103, 1, '2025-08-31 13:56:15.46813', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029462946648069, '在线办理材料管理修改', 1962029462946648066, 3, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblMaterial:edit', '#', 103, 1, '2025-08-31 13:56:15.518082', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029462946648070, '在线办理材料管理删除', 1962029462946648066, 4, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblMaterial:remove', '#', 103, 1, '2025-08-31 13:56:15.568097', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029462946648071, '在线办理材料管理导出', 1962029462946648066, 5, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblMaterial:export', '#', 103, 1, '2025-08-31 13:56:15.618656', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029465819746306, '智桂通-在线办理配置', 1929410395467423746, 12, 'gzsxZxblConfig', 'gongzheng/basicdata/zxbl/zxblpz/index', NULL, '1', '0', 'C', '0', '0', 'basicdata:gzsxZxblConfig:list', 'list', 103, 1, '2025-08-31 13:56:06', 1, '2025-08-31 14:00:35.015', '在线办理配置菜单');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029465819746307, '在线办理配置查询', 1962029465819746306, 1, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblConfig:query', '#', 103, 1, '2025-08-31 13:56:06.508691', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029465819746308, '在线办理配置新增', 1962029465819746306, 2, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblConfig:add', '#', 103, 1, '2025-08-31 13:56:06.558085', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029465819746309, '在线办理配置修改', 1962029465819746306, 3, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblConfig:edit', '#', 103, 1, '2025-08-31 13:56:06.60858', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029465819746310, '在线办理配置删除', 1962029465819746306, 4, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblConfig:remove', '#', 103, 1, '2025-08-31 13:56:06.661068', NULL, NULL, '');

INSERT INTO "public"."sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query_param", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (1962029465819746311, '在线办理配置导出', 1962029465819746306, 5, '#', '', NULL, '1', '0', 'F', '0', '0', 'basicdata:gzsxZxblConfig:export', '#', 103, 1, '2025-08-31 13:56:06.711021', NULL, NULL, '');

UPDATE "public"."sys_menu" SET "menu_name" = '提存款申请', "parent_id" = 1936434006321528833, "order_num" = 20, "path" = 'tcsq', "component" = 'gongzheng/bzfz/tck/tcsq/index', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'bzfz:tcsq:query', "icon" = 'tab', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 16:28:08', "update_by" = 1, "update_time" = '2025-08-28 09:44:38.674', "remark" = '' WHERE "menu_id" = 1954459737206734849;

UPDATE "public"."sys_menu" SET "menu_name" = '提存款审批', "parent_id" = 1936434006321528833, "order_num" = 30, "path" = 'tcsp', "component" = 'gongzheng/bzfz/tck/tcsp/index', "query_param" = NULL, "is_frame" = '1', "is_cache" = '0', "menu_type" = 'C', "visible" = '0', "status" = '0', "perms" = 'bzfz:txsp:query', "icon" = 'tab', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-10 16:29:29', "update_by" = 1, "update_time" = '2025-08-28 09:44:23.908', "remark" = '' WHERE "menu_id" = 1954460080208527361;

UPDATE "public"."sys_oss_config" SET "tenant_id" = '000000', "config_key" = 'minio', "access_key" = '74t2rW83ngzvXjdBcjYg', "secret_key" = 'sgQGDja6o3Tc9yMn2Hi5EG0JMyZxkwEVk0txa4mb', "bucket_name" = 'gzyw', "prefix" = '', "endpoint" = '***************:9104', "domain" = '', "is_https" = 'N', "region" = '', "access_policy" = '0', "status" = '0', "ext1" = '', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-06-02 12:32:37.510297', "update_by" = 1, "update_time" = '2025-07-05 10:59:23.898', "remark" = '' WHERE "oss_config_id" = 1;

DELETE FROM "public"."sys_role_menu" WHERE "role_id" = 1954502146447781889 AND "menu_id" = 117;

DELETE FROM "public"."sys_role_menu" WHERE "role_id" = 1954502146447781889 AND "menu_id" = 120;

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1933761494087553026, 1961055270076010497);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1933761494087553026, 1961055270076010498);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1933761494087553026, 1961055270076010499);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 6);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 121);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 122);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1606);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1607);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1608);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1609);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1610);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1611);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1612);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1613);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1614);

INSERT INTO "public"."sys_role_menu" ("role_id", "menu_id") VALUES (1954502146447781889, 1615);


UPDATE "public"."gz_mb_jcxx" SET "title" = '通用告知书', "template_type" = 0, "yw_id" = 1941842378963513346, "del_flag" = '0', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-07-29 22:24:58.787', "update_by" = 1, "update_time" = '2025-07-29 22:24:59.554', "remark" = NULL, "tenant_id" = '000000', "classify" = 2, "default_status" = 1, "gzlb" = 116 WHERE "id" = 1950200885674573825;

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958520050256822273, 1941416346785472513, 'ff8080816b098258016b0d10c2774e49.doc', 6, '2025/08/21/6c720fe5338a4adfb53f05428af07d32.doc', 99, '1', 100, 1, '2025-08-21 21:22:22.185', 1, '2025-08-21 21:22:23.348', NULL, '000000', 1, 1958520041247457282);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958806438940332034, 1952963014219624450, '公证企业申请表.doc', 8, '2025/08/22/7ff9fff0d5724d7c8e2124072d908ce9.doc', NULL, '1', 100, 1, '2025-08-22 16:20:22.566', 1, '2025-08-22 17:18:44.863', NULL, '000000', 0, 1958806427984809986);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958806497450872834, 1950198701817921538, '公证自然人申请表.doc', 8, '2025/08/22/19df5897056f4100ae2b0cc76453a09e.doc', NULL, '1', 100, 1, '2025-08-22 16:20:36.518', 1, '2025-08-22 16:20:38.079', NULL, '000000', 1, 1958806488718331905);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958810280129986561, 1950198701817921538, '公证自然人申请表.doc', 8, '2025/08/22/f73c186e3e1848baa97c3edbdbe32018.doc', NULL, '1', 100, 1, '2025-08-22 16:35:38.387', 1, '2025-08-22 16:35:39.941', NULL, '000000', 1, 1958810269774249986);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958821125136412674, 1950198701817921538, '公证自然人申请表.doc', 8, '2025/08/22/de39ec6c34fd475e989b1084bc6001be.doc', NULL, '0', 100, 1, '2025-08-22 17:18:44.027', 1, '2025-08-22 17:18:45.58', NULL, '000000', 1, 1958821117838323713);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958889890192576514, 1941416346785472513, '个人信息表.doc', 6, '2025/08/22/e5aa9d5300cd45a98680b022b7284c77.doc', 99, '1', 100, 1, '2025-08-22 21:51:58.899', 1, '2025-08-22 21:51:59.927', NULL, '000000', 1, 1958889884576403457);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1958896064371642369, 1941416346785472513, '个人信息表.doc', 6, '2025/08/22/9bcd02848a524296ae93470ee2a43859.doc', 99, '0', 100, 1, '2025-08-22 22:16:30.947', 1, '2025-08-22 22:16:31.974', NULL, '000000', 1, 1958896055857205249);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1959216398777991170, 1952963014219624450, '公证企业申请表 (1).doc', 8, '2025/08/23/4a43bada3290416ea80911110bfd31db.doc', NULL, '0', 100, 1, '2025-08-23 19:29:24.618', 1, '2025-08-23 19:29:24.618', NULL, '000000', 0, 1959216391333101569);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1960359279584677889, 1950199349754003457, '现场记录.doc', 23, '2025/08/26/69c363fe1a6046b1b74acc892a84f602.doc', NULL, '1', 100, 1, '2025-08-26 23:10:48.634', 1, '2025-08-26 23:10:49.914', NULL, '000000', 1, 1960359265189826561);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1960362914876043266, 1950199349754003457, '现场记录.doc', 23, '2025/08/26/87655ea09f594faf802a15929424fb4e.doc', NULL, '1', 100, 1, '2025-08-26 23:25:15.349', 1, '2025-08-26 23:25:16.813', NULL, '000000', 1, 1960362903413010434);

INSERT INTO "public"."gz_mb_wd" ("id", "mb_id", "wd_mc", "wd_lb", "wd_dz", "yw_id", "del_flag", "create_dept", "create_by", "create_time", "update_by", "update_time", "remark", "tenant_id", "is_default", "wd_oss_id") VALUES (1960368005972602881, 1950199349754003457, '现场记录.doc', 23, '2025/08/26/9a951848f0004b079249e66893ec9ec0.doc', NULL, '0', 100, 1, '2025-08-26 23:45:29.166', 1, '2025-08-26 23:45:30.591', NULL, '000000', 1, 1960367987219869698);

UPDATE "public"."gz_mb_wd" SET "mb_id" = 1941416346785472513, "wd_mc" = '个人信息表', "wd_lb" = 6, "wd_dz" = '1947652449880309761', "yw_id" = 99, "del_flag" = '1', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-07-29 21:34:17.157', "update_by" = 1, "update_time" = '2025-07-29 21:34:18.249', "remark" = NULL, "tenant_id" = '000000', "is_default" = 1, "wd_oss_id" = 1947652449880309761 WHERE "id" = 1950188128157831170;

UPDATE "public"."gz_mb_wd" SET "mb_id" = 1950199349754003457, "wd_mc" = 'ff8080816b098258016b0d1028af4a5a.doc', "wd_lb" = 23, "wd_dz" = '2025/07/29/bf675e7fa49b4010aa613967f9bbe411.doc', "yw_id" = NULL, "del_flag" = '1', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-07-29 22:18:52.947', "update_by" = 1, "update_time" = '2025-07-29 22:18:53.758', "remark" = NULL, "tenant_id" = '000000', "is_default" = 1, "wd_oss_id" = 1950199343475130370 WHERE "id" = 1950199351247175682;

UPDATE "public"."gz_mb_wd" SET "mb_id" = 1950200885674573825, "wd_mc" = '告知书.doc', "wd_lb" = 116, "wd_dz" = '2025/07/29/0f15186ecc784cc4bbc8d7bf77c5e0c4.doc', "yw_id" = 1941842378963513346, "del_flag" = '0', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-07-29 22:24:59.157', "update_by" = 1, "update_time" = '2025-07-29 22:25:00.186', "remark" = NULL, "tenant_id" = '000000', "is_default" = 1, "wd_oss_id" = 1950200875893456897 WHERE "id" = 1950200887230660609;

UPDATE "public"."gz_mb_wd" SET "mb_id" = 1952963014219624450, "wd_mc" = 'ff80808166eef1020166f2af722521d3.doc', "wd_lb" = 8, "wd_dz" = '2025/08/06/beba6b09b6474dd48750f8bf362bf3f2.doc', "yw_id" = NULL, "del_flag" = '1', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-06 13:20:41.731', "update_by" = 1, "update_time" = '2025-08-06 13:21:52.178', "remark" = NULL, "tenant_id" = '000000', "is_default" = 0, "wd_oss_id" = 1952962966220009474 WHERE "id" = 1952963014941044737;

UPDATE "public"."gz_mb_wd" SET "mb_id" = 1950198701817921538, "wd_mc" = 'ff80808166eef1020166f2af724721db.doc', "wd_lb" = 8, "wd_dz" = '2025/08/06/8a681905d1014a9ebe9209ecac2632af.doc', "yw_id" = NULL, "del_flag" = '1', "create_dept" = 100, "create_by" = 1, "create_time" = '2025-08-06 13:21:51.457', "update_by" = 1, "update_time" = '2025-08-06 13:21:52.865', "remark" = NULL, "tenant_id" = '000000', "is_default" = 1, "wd_oss_id" = 1952963302276034562 WHERE "id" = 1952963307426639873;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955444019592224770;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955444071953915906;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955444076328574977;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955444200026988546;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955444288719740929;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955444943433818114;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955448629602082817;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955450889191088129;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955453351490805762;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955454235700420610;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955454385311244290;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955454436423032834;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955454484162600961;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955454681479438338;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955454986266927105;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955455320603287554;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458248298782722;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458518265159681;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458613442306049;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458665023856642;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458716710264833;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458793491193858;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955458843420188674;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955459890104557570;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1955528363757330434;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1956260882077245442;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957283086952857601;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957283289571295233;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957283554231877634;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957283697945509889;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957283916858818561;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957311115175428097;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957312006280478721;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957312139617402881;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957313527063805953;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957314519054450690;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957314545726029826;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957314573899169793;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957314598356156418;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957314870495182850;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957356293575704578;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957356491626545154;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957357893346164738;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957358289737252865;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957358383949709314;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957358471988150273;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957358881775845378;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957371684695867393;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957373863989116930;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957374293032861698;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957374419432407042;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957425475344433153;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1957706143232196609;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1958704977475317761;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1959494076226383873;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1959619466072731649;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1959640615821291522;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1959992578664607746;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1960179847556034562;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1960248411654778881;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1960248525601435649;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1960248953282031617;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1960249916621385729;

DELETE FROM "public"."sys_oss" WHERE "oss_id" = 1960524075964362753;

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955476465474633730, '000000', '2025/08/13/0423885eceee42c18380b8af6b0b4011.doc', 'maker1755056878322_jlffXhNy.doc', '.doc', 'http://***************:9104/gzyw/2025/08/13/0423885eceee42c18380b8af6b0b4011.doc', '', 103, 1, '2025-08-13 11:48:15.023', 1, '2025-08-13 11:48:15.023', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955606588367073282, '000000', '2025/08/13/07c354feb2ee4ca58576baf7eb7358c2.docx', '解除协议.docx', '.docx', 'http://***************:9104/gzyw/2025/08/13/07c354feb2ee4ca58576baf7eb7358c2.docx', 'code=oss', 102, 1935515056178139138, '2025-08-13 20:25:18.74', 1935515056178139138, '2025-08-13 20:25:18.74', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955626201981956097, '000000', '2025/08/13/53dbf39e16b74140857f44c957fef09f.jpeg', '4f3f922780b5cb487f50bd2f7a923d53.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/13/53dbf39e16b74140857f44c957fef09f.jpeg', 'code=oss', 100, 1, '2025-08-13 21:43:14.989', 1, '2025-08-13 21:43:14.989', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955626300573265922, '000000', '2025/08/13/fe8ff59781ee477293b2ae265c6391b2.png', '2025-08-13-21-43-38_18056.png', '.png', 'http://***************:9104/gzyw/2025/08/13/fe8ff59781ee477293b2ae265c6391b2.png', 'code=oss', 100, 1, '2025-08-13 21:43:38.493', 1, '2025-08-13 21:43:38.493', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955633346848796674, '000000', '2025/08/13/ab70bf6880c2468f8d81a4f718d41d43.pdf', '测试pdf.pdf', '.pdf', 'http://***************:9104/gzyw/2025/08/13/ab70bf6880c2468f8d81a4f718d41d43.pdf', 'code=oss', 100, 1, '2025-08-13 22:11:38.463', 1, '2025-08-13 22:11:38.463', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955638025544577026, '000000', '2025/08/13/a6ad4b93f1664cc8bd4e107cfea14ef1.pdf', '测试pdf31313132132123131645164.pdf', '.pdf', 'http://***************:9104/gzyw/2025/08/13/a6ad4b93f1664cc8bd4e107cfea14ef1.pdf', 'code=oss', 100, 1, '2025-08-13 22:30:13.958', 1, '2025-08-13 22:30:13.958', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955649135320600577, '000000', '2025/08/13/85a8809e4d6c45d59e71b2bcb7289839.mp3', '一百万个可能.mp3', '.mp3', 'http://***************:9104/gzyw/2025/08/13/85a8809e4d6c45d59e71b2bcb7289839.mp3', 'code=oss', 100, 1, '2025-08-13 23:14:22.731', 1, '2025-08-13 23:14:22.731', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955651163342082049, '000000', '2025/08/13/115f7dd447de4e8bbb4c642c0a145101.mp4', '当妈妈不在家.mp4', '.mp4', 'http://***************:9104/gzyw/2025/08/13/115f7dd447de4e8bbb4c642c0a145101.mp4', 'code=oss', 100, 1, '2025-08-13 23:22:26.251', 1, '2025-08-13 23:22:26.251', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955824899624853505, '000000', '2025/08/14/b5cc02bc4b894353be815f77b56608d6.jpeg', '3d8a16d11944689f9f210d2675ead230.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/14/b5cc02bc4b894353be815f77b56608d6.jpeg', 'code=oss', 103, 1, '2025-08-14 10:52:48.197', 1, '2025-08-14 10:52:48.197', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1955824945871249409, '000000', '2025/08/14/16f9482761bd409695bc09c1d3079c00.jpeg', '6ac54ccb31a09a5c1223677ba07c283f.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/14/16f9482761bd409695bc09c1d3079c00.jpeg', 'code=oss', 103, 1, '2025-08-14 10:52:59.223', 1, '2025-08-14 10:52:59.223', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956163599294631938, '000000', '2025/08/15/65779b2fd9dc45678e5b8d22cb5e7696.doc', 'maker1755220706010_yktEb2C4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/15/65779b2fd9dc45678e5b8d22cb5e7696.doc', '', 103, 1, '2025-08-15 09:18:40.491', 1, '2025-08-15 09:18:40.491', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956163724914036737, '000000', '2025/08/15/740c415a32a34d0a8ce1f352f4f6786f.doc', 'maker1755220742481_ICz0Osjq.doc', '.doc', 'http://***************:9104/gzyw/2025/08/15/740c415a32a34d0a8ce1f352f4f6786f.doc', '', 103, 1, '2025-08-15 09:19:10.441', 1, '2025-08-15 09:19:10.441', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956187779687878657, '000000', '2025/08/15/b5e117a16b5b42abb73990b8f96f8534.doc', 'maker1755226472749_1EcT4k1K.doc', '.doc', 'http://***************:9104/gzyw/2025/08/15/b5e117a16b5b42abb73990b8f96f8534.doc', '', 103, 1, '2025-08-15 10:54:45.55', 1, '2025-08-15 10:54:45.55', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956188418560069633, '000000', '2025/08/15/dfd6131398514df79ea1e5644c17e6c9.doc', 'maker1755226629650_2M7C2lxo.doc', '.doc', 'http://***************:9104/gzyw/2025/08/15/dfd6131398514df79ea1e5644c17e6c9.doc', '', 103, 1, '2025-08-15 10:57:17.88', 1, '2025-08-15 10:57:17.88', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956201125074014209, '000000', '2025/08/15/5571585e7d684eceb0e2e99f0ddc5a6a.doc', 'maker1755229650330_lehnKiTn.doc', '.doc', 'http://***************:9104/gzyw/2025/08/15/5571585e7d684eceb0e2e99f0ddc5a6a.doc', '', 103, 1, '2025-08-15 11:47:47.341', 1, '2025-08-15 11:47:47.341', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956252989647851521, '000000', '2025/08/15/2c56fc02a8924269a6d7177e2fb4cbdc.docx', '公证在线业务H5页面设计.docx', '.docx', 'http://***************:9104/gzyw/2025/08/15/2c56fc02a8924269a6d7177e2fb4cbdc.docx', 'code=oss', 103, 1, '2025-08-15 15:13:52.813', 1, '2025-08-15 15:13:52.813', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956257190801149954, '000000', '2025/08/15/12493b44dee243309d024b6b920d3ed4.docx', '公证在线业务H5页面设计.docx', '.docx', 'http://***************:9104/gzyw/2025/08/15/12493b44dee243309d024b6b920d3ed4.docx', 'code=oss', 103, 1, '2025-08-15 15:30:34.445', 1, '2025-08-15 15:30:34.445', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956257427590582274, '000000', '2025/08/15/57829dc2ed0044cda10dfc4394e9867b.docx', '公证在线业务H5页面设计.docx', '.docx', 'http://***************:9104/gzyw/2025/08/15/57829dc2ed0044cda10dfc4394e9867b.docx', 'code=oss', 103, 1, '2025-08-15 15:31:30.9', 1, '2025-08-15 15:31:30.9', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956686008025374721, '000000', '2025/08/16/c0ae1f37ee0e43d18f0343668c14a1cf.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/c0ae1f37ee0e43d18f0343668c14a1cf.docx', 'code=oss', 100, 1, '2025-08-16 19:54:32.439', 1, '2025-08-16 19:54:32.439', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956686527687057409, '000000', '2025/08/16/5f2337d5e1914b59bf38a92a7ca543aa.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/5f2337d5e1914b59bf38a92a7ca543aa.docx', 'code=oss', 100, 1, '2025-08-16 19:56:36.336', 1, '2025-08-16 19:56:36.336', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956688528168738818, '000000', '2025/08/16/2b22b931ad3a498f959c510f825a3d5c.png', '2025-08-16-20-04-32_72834.png', '.png', 'http://***************:9104/gzyw/2025/08/16/2b22b931ad3a498f959c510f825a3d5c.png', 'code=oss', 100, 1, '2025-08-16 20:04:33.287', 1, '2025-08-16 20:04:33.287', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956688682863058945, '000000', '2025/08/16/44d2d620b45c4f7fb1d847630d546700.mp4', '当妈妈不在家.mp4', '.mp4', 'http://***************:9104/gzyw/2025/08/16/44d2d620b45c4f7fb1d847630d546700.mp4', 'code=oss', 100, 1, '2025-08-16 20:05:10.169', 1, '2025-08-16 20:05:10.169', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956688766858190850, '000000', '2025/08/16/52c1bebea804436abcc38fac0df6a330.pdf', '测试pdf31313132132123131645164.pdf', '.pdf', 'http://***************:9104/gzyw/2025/08/16/52c1bebea804436abcc38fac0df6a330.pdf', 'code=oss', 100, 1, '2025-08-16 20:05:30.195', 1, '2025-08-16 20:05:30.195', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689119771123714, '000000', '2025/08/16/f7944a46527c4bd98ea5142356e78824.docx', '新建 DOCX 文档.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/f7944a46527c4bd98ea5142356e78824.docx', 'code=oss', 100, 1, '2025-08-16 20:06:54.336', 1, '2025-08-16 20:06:54.336', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689349912584193, '000000', '2025/08/16/666ef08e7a374e12ab81dfd4ae0de747.doc', 'maker1755346060581_H4SC0dFw.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/666ef08e7a374e12ab81dfd4ae0de747.doc', '', 100, 1, '2025-08-16 20:07:49.206', 1, '2025-08-16 20:07:49.206', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689394720333826, '000000', '2025/08/16/af3e0284a5aa420aa7b5dc6d0beafeba.doc', 'maker1755346076579_TDFrf1aX.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/af3e0284a5aa420aa7b5dc6d0beafeba.doc', '', 100, 1, '2025-08-16 20:07:59.889', 1, '2025-08-16 20:07:59.889', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689438265597954, '000000', '2025/08/16/b2cee7e16f66462cb7026439b8862780.doc', 'maker1755346086274_DEulhT2G.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/b2cee7e16f66462cb7026439b8862780.doc', '', 100, 1, '2025-08-16 20:08:10.271', 1, '2025-08-16 20:08:10.271', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689492179181569, '000000', '2025/08/16/8d2c59e3f38c4c2daed5d8c8c5ef807e.doc', 'maker1755346099332_aQ8rYVkk.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/8d2c59e3f38c4c2daed5d8c8c5ef807e.doc', '', 100, 1, '2025-08-16 20:08:23.125', 1, '2025-08-16 20:08:23.125', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689685180080129, '000000', '2025/08/16/c72eeac3ab034cd9a02ea71bfd8af4a2.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/c72eeac3ab034cd9a02ea71bfd8af4a2.docx', 'code=oss', 100, 1, '2025-08-16 20:09:09.141', 1, '2025-08-16 20:09:09.141', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956689779560308737, '000000', '2025/08/16/9336adecaa6d4b318d9d2b8eb7797e1a.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/9336adecaa6d4b318d9d2b8eb7797e1a.docx', 'code=oss', 100, 1, '2025-08-16 20:09:31.642', 1, '2025-08-16 20:09:31.642', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956690953097207810, '000000', '2025/08/16/e673567cb8784574bc1ebee3a0a0a74f.docx', '新建 DOCX 文档.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/e673567cb8784574bc1ebee3a0a0a74f.docx', 'code=oss', 100, 1, '2025-08-16 20:14:11.435', 1, '2025-08-16 20:14:11.435', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691012169785346, '000000', '2025/08/16/fce692e5a74b4bd78e980ff1c4bf98d7.doc', 'maker1755346461347_dtvA2VVG.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/fce692e5a74b4bd78e980ff1c4bf98d7.doc', '', 100, 1, '2025-08-16 20:14:25.519', 1, '2025-08-16 20:14:25.519', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691045749383169, '000000', '2025/08/16/62ad9b32950944d0948630388468e2a0.doc', 'maker1755346469691_wTPQpMPe.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/62ad9b32950944d0948630388468e2a0.doc', '', 100, 1, '2025-08-16 20:14:33.525', 1, '2025-08-16 20:14:33.525', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691083506507777, '000000', '2025/08/16/61e2fbcbf0a745829b71981724a8a80d.doc', 'maker1755346479879_T2fTrtmm.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/61e2fbcbf0a745829b71981724a8a80d.doc', '', NULL, 1, '2025-08-16 20:14:42.527', NULL, '2025-08-16 20:14:42.527', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691122811330561, '000000', '2025/08/16/6fb0d8c072c143f7909dc3bd41a8625f.doc', 'maker1755346488634_VJLDJEyy.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/6fb0d8c072c143f7909dc3bd41a8625f.doc', '', 100, 1, '2025-08-16 20:14:51.898', 1, '2025-08-16 20:14:51.898', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691158852984834, '000000', '2025/08/16/706d78461b6d4c168a2af67f0b9ed002.doc', 'maker1755346495818_arBeEGnl.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/706d78461b6d4c168a2af67f0b9ed002.doc', '', 100, 1, '2025-08-16 20:15:00.491', 1, '2025-08-16 20:15:00.491', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691198942142466, '000000', '2025/08/16/6ceeb8af811d48ecb3e0fd01e907abde.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/16/6ceeb8af811d48ecb3e0fd01e907abde.docx', 'code=oss', 100, 1, '2025-08-16 20:15:10.049', 1, '2025-08-16 20:15:10.049', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691317733220354, '000000', '2025/08/16/abf1e6be4a474e33a475a52c6f4535d2.doc', 'maker1755346535160_i5xVnyZB.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/abf1e6be4a474e33a475a52c6f4535d2.doc', '', 100, 1, '2025-08-16 20:15:38.371', 1, '2025-08-16 20:15:38.371', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956691382438748162, '000000', '2025/08/16/f8b07fcc4aab47bfb9bdbe966134e8dc.doc', 'maker1755346549458_LR8AbkRM.doc', '.doc', 'http://***************:9104/gzyw/2025/08/16/f8b07fcc4aab47bfb9bdbe966134e8dc.doc', '', 100, 1, '2025-08-16 20:15:53.799', 1, '2025-08-16 20:15:53.799', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1956955013016735745, '000000', '2025/08/17/9d8ad7b17bf9497e85fe3746bae8ce00.txt', 'nginx.txt', '.txt', 'http://***************:9104/gzyw/2025/08/17/9d8ad7b17bf9497e85fe3746bae8ce00.txt', 'code=oss', 100, 1, '2025-08-17 13:43:28.231', 1, '2025-08-17 13:43:28.231', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957057417755045890, '000000', '2025/08/17/66bc41c8c0ad48a98181d19d8a000acc.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/66bc41c8c0ad48a98181d19d8a000acc.docx', 'code=oss', 100, 1, '2025-08-17 20:30:23.418', 1, '2025-08-17 20:30:23.418', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957060669322113026, '000000', '2025/08/17/83029989d1df43c3a9913c74a332786d.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/83029989d1df43c3a9913c74a332786d.docx', 'code=oss', 100, 1, '2025-08-17 20:43:18.652', 1, '2025-08-17 20:43:18.652', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957060890609397762, '000000', '2025/08/17/032dc53eb97f44018307ff6c75422e7a.docx', '新建 DOCX 文档.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/032dc53eb97f44018307ff6c75422e7a.docx', 'code=oss', 100, 1, '2025-08-17 20:44:11.411', 1, '2025-08-17 20:44:11.411', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957060980015181825, '000000', '2025/08/17/35679cad7da44d1192df79e98717fb52.docx', '新建 DOCX 文档 - 副本.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/35679cad7da44d1192df79e98717fb52.docx', 'code=oss', 100, 1, '2025-08-17 20:44:32.727', 1, '2025-08-17 20:44:32.727', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957061078270947329, '000000', '2025/08/17/6ba09152b6c24a9fb3adda28cd0932d1.docx', '测试文档1.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/6ba09152b6c24a9fb3adda28cd0932d1.docx', 'code=oss', 100, 1, '2025-08-17 20:44:56.153', 1, '2025-08-17 20:44:56.153', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957061821111545858, '000000', '2025/08/17/06c814a7f9c44f2cbd23c7c5a37e4f83.docx', '测试文档1.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/06c814a7f9c44f2cbd23c7c5a37e4f83.docx', 'code=oss', 100, 1, '2025-08-17 20:47:53.26', 1, '2025-08-17 20:47:53.26', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957062012543774721, '000000', '2025/08/17/dc2bf62ef91b4b7badfd046087316fd3.docx', '测试文档1.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/dc2bf62ef91b4b7badfd046087316fd3.docx', 'code=oss', 100, 1, '2025-08-17 20:48:38.902', 1, '2025-08-17 20:48:38.902', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957075311708680193, '000000', '2025/08/17/b6a69cfa99e1497f837ae9b1d6e714f4.doc', 'maker1755438083745_V6tGRIo2.doc', '.doc', 'http://***************:9104/gzyw/2025/08/17/b6a69cfa99e1497f837ae9b1d6e714f4.doc', '', 100, 1, '2025-08-17 21:41:29.669', 1, '2025-08-17 21:41:29.669', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957088933456314369, '000000', '2025/08/17/291edcf8bea44c62bc1d9b6ce0fa14c7.docx', '测试空文件.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/291edcf8bea44c62bc1d9b6ce0fa14c7.docx', 'code=oss', NULL, 1954948962406567938, '2025-08-17 22:35:37.354', NULL, '2025-08-17 22:35:37.354', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957090478064476162, '000000', '2025/08/17/ac550fd169d5424fbc542ec5c7204a05.doc', '测试文档.doc', '.doc', 'http://***************:9104/gzyw/2025/08/17/ac550fd169d5424fbc542ec5c7204a05.doc', 'code=oss', NULL, 1, '2025-08-17 22:41:45.611', NULL, '2025-08-17 22:41:45.611', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957092829994090498, '000000', '2025/08/17/cb785cba151947c9b799f1384fe10b44.doc', 'maker1755442260258_SDhtLi76.doc', '.doc', 'http://***************:9104/gzyw/2025/08/17/cb785cba151947c9b799f1384fe10b44.doc', '', 100, 1954948962406567938, '2025-08-17 22:51:06.358', 1954948962406567938, '2025-08-17 22:51:06.358', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957092926895095809, '000000', '2025/08/17/117628d915804df994ac52b2f5528e0c.doc', 'maker1755442283242_Gid73lXc.doc', '.doc', 'http://***************:9104/gzyw/2025/08/17/117628d915804df994ac52b2f5528e0c.doc', '', 100, 1954948962406567938, '2025-08-17 22:51:29.469', 1954948962406567938, '2025-08-17 22:51:29.469', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957093234211749890, '000000', '2025/08/17/730ced6907fb4b7a9b706f635b6dceed.doc', 'maker1755442356569_Q5lmsb1I.doc', '.doc', 'http://***************:9104/gzyw/2025/08/17/730ced6907fb4b7a9b706f635b6dceed.doc', '', NULL, 1954948962406567938, '2025-08-17 22:52:42.735', NULL, '2025-08-17 22:52:42.735', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957094005183545346, '000000', '2025/08/17/f51fadffc92849e496a31e2b9e5729c0.doc', 'maker1755442539665_rOxhG2W5.doc', '.doc', 'http://***************:9104/gzyw/2025/08/17/f51fadffc92849e496a31e2b9e5729c0.doc', '', 100, 1954948962406567938, '2025-08-17 22:55:46.543', 1954948962406567938, '2025-08-17 22:55:46.543', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957094737995563010, '000000', '2025/08/17/210275b44a134babbd5d6bec200e9036.docx', '测试空文件.docx', '.docx', 'http://***************:9104/gzyw/2025/08/17/210275b44a134babbd5d6bec200e9036.docx', 'code=oss', NULL, 1954948962406567938, '2025-08-17 22:58:41.261', NULL, '2025-08-17 22:58:41.261', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957119718553407490, '000000', '2025/08/18/1adcd56a4fcc432f9ab537cf20e9bb82.doc', 'maker1755448648916_vTy3PmHW.doc', '.doc', 'http://***************:9104/gzyw/2025/08/18/1adcd56a4fcc432f9ab537cf20e9bb82.doc', '', 100, 1, '2025-08-18 00:37:57.094', 1, '2025-08-18 00:37:57.094', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957120232905101314, '000000', '2025/08/18/851a2c7c2d4f4fd3854866ae25aa5b11.doc', 'maker1755448784896_07kYYDrA.doc', '.doc', 'http://***************:9104/gzyw/2025/08/18/851a2c7c2d4f4fd3854866ae25aa5b11.doc', '', 100, 1, '2025-08-18 00:39:59.725', 1, '2025-08-18 00:39:59.725', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957120382457204737, '000000', '2025/08/18/810cbe4f91954cea855ad3c8dab29076.doc', 'maker1755448819091_YGFntEpd.doc', '.doc', 'http://***************:9104/gzyw/2025/08/18/810cbe4f91954cea855ad3c8dab29076.doc', '', 100, 1, '2025-08-18 00:40:35.377', 1, '2025-08-18 00:40:35.377', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957281668276506626, '000000', '2025/08/18/03e1cb78197a4a2287a154e21c0afea0.mp4', 'VID_20250818_111106.mp4', '.mp4', 'http://***************:9104/gzyw/2025/08/18/03e1cb78197a4a2287a154e21c0afea0.mp4', 'code=oss', 103, 1, '2025-08-18 11:21:28.909', 1, '2025-08-18 11:21:28.909', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957281671933939714, '000000', '2025/08/18/3b40c658d08a40d8837471e3890e0b65.mp4', 'VID_20250818_111106 - 副本.mp4', '.mp4', 'http://***************:9104/gzyw/2025/08/18/3b40c658d08a40d8837471e3890e0b65.mp4', 'code=oss', 103, 1, '2025-08-18 11:21:29.796', 1, '2025-08-18 11:21:29.796', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957281943087304706, '000000', '2025/08/18/d11bafdead784507b1d084854573c8fa.jpeg', '3d8a16d11944689f9f210d2675ead230.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/d11bafdead784507b1d084854573c8fa.jpeg', 'code=oss', 103, 1, '2025-08-18 11:22:34.43', 1, '2025-08-18 11:22:34.43', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957281943921971202, '000000', '2025/08/18/1d5cf53000d842fbac3ee087d3df6d1f.jpeg', '8f1af9cbaa9d9aee17a0c0bd944ef662 - 副本.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/1d5cf53000d842fbac3ee087d3df6d1f.jpeg', 'code=oss', 103, 1, '2025-08-18 11:22:34.629', 1, '2025-08-18 11:22:34.629', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957281944496590849, '000000', '2025/08/18/eece103aa0d44e7d8b2242b99a7d9152.jpeg', '6ac54ccb31a09a5c1223677ba07c283f.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/eece103aa0d44e7d8b2242b99a7d9152.jpeg', 'code=oss', 103, 1, '2025-08-18 11:22:34.773', 1, '2025-08-18 11:22:34.773', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352713075298306, '000000', '2025/08/18/1dd1ccef641b4a5684e6785608061c5d.jpeg', '8f1af9cbaa9d9aee17a0c0bd944ef662.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/1dd1ccef641b4a5684e6785608061c5d.jpeg', 'code=oss', 103, 1, '2025-08-18 16:03:47.309', 1, '2025-08-18 16:03:47.309', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352713075298307, '000000', '2025/08/18/237ad8a7709c4c07976d2e8cb967ca9e.jpeg', '6ac54ccb31a09a5c1223677ba07c283f.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/237ad8a7709c4c07976d2e8cb967ca9e.jpeg', 'code=oss', 103, 1, '2025-08-18 16:03:47.309', 1, '2025-08-18 16:03:47.309', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352713075298308, '000000', '2025/08/18/5ca17799c2ab4a07bfea1adac243283c.jpeg', '8f1af9cbaa9d9aee17a0c0bd944ef662 - 副本.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/5ca17799c2ab4a07bfea1adac243283c.jpeg', 'code=oss', 103, 1, '2025-08-18 16:03:47.309', 1, '2025-08-18 16:03:47.309', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352713075298309, '000000', '2025/08/18/80d3635ea7ea4bc69b021b2f8b6ddcef.jpeg', '3d8a16d11944689f9f210d2675ead230.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/80d3635ea7ea4bc69b021b2f8b6ddcef.jpeg', 'code=oss', 103, 1, '2025-08-18 16:03:47.309', 1, '2025-08-18 16:03:47.309', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352937625751554, '000000', '2025/08/18/d3156bb1e6f34cf294a6e2831af083ff.jpeg', '6ac54ccb31a09a5c1223677ba07c283f.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/d3156bb1e6f34cf294a6e2831af083ff.jpeg', 'code=oss', 103, 1, '2025-08-18 16:04:40.858', 1, '2025-08-18 16:04:40.858', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352937625751555, '000000', '2025/08/18/34e9e767435849ada29681bb330d5271.jpeg', '3d8a16d11944689f9f210d2675ead230.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/34e9e767435849ada29681bb330d5271.jpeg', 'code=oss', 103, 1, '2025-08-18 16:04:40.858', 1, '2025-08-18 16:04:40.858', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352937726414850, '000000', '2025/08/18/03d4507c664041fb913b1a74eed56277.jpeg', '8f1af9cbaa9d9aee17a0c0bd944ef662.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/03d4507c664041fb913b1a74eed56277.jpeg', 'code=oss', 103, 1, '2025-08-18 16:04:40.874', 1, '2025-08-18 16:04:40.874', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957352937726414851, '000000', '2025/08/18/93e6021024964ad891af3d37dd1d5ebd.jpeg', '8f1af9cbaa9d9aee17a0c0bd944ef662 - 副本.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/18/93e6021024964ad891af3d37dd1d5ebd.jpeg', 'code=oss', 103, 1, '2025-08-18 16:04:40.874', 1, '2025-08-18 16:04:40.874', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957747050972532737, '000000', '2025/08/19/2764cc85b91540c9a83c53ceaa299bb0.doc', 'maker1755598220760_NoibBcfZ.doc', '.doc', 'http://***************:9104/gzyw/2025/08/19/2764cc85b91540c9a83c53ceaa299bb0.doc', '', 100, 1, '2025-08-19 18:10:44.794', 1, '2025-08-19 18:10:44.794', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957747841745637378, '000000', '2025/08/19/df52ad904eaa4e5995da4dd3de7656ba.doc', 'maker1755598412647_f7wuvI7d.doc', '.doc', 'http://***************:9104/gzyw/2025/08/19/df52ad904eaa4e5995da4dd3de7656ba.doc', '', 100, 1, '2025-08-19 18:13:53.34', 1, '2025-08-19 18:13:53.34', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957748260651749378, '000000', '2025/08/19/e32fd1aac82a45baba3a134bcf3ee2d0.doc', 'maker1755598513284_TUC7e2h1.doc', '.doc', 'http://***************:9104/gzyw/2025/08/19/e32fd1aac82a45baba3a134bcf3ee2d0.doc', '', 100, 1, '2025-08-19 18:15:33.208', 1, '2025-08-19 18:15:33.208', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957973326660435970, '000000', '2025/08/20/3e15d66cc1c34684b93e5817dc593205.doc', 'maker1755652174035_hk3f04B4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/3e15d66cc1c34684b93e5817dc593205.doc', '', 100, 1, '2025-08-20 09:09:53.121', 1, '2025-08-20 09:09:53.121', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957975988348997634, '000000', '2025/08/20/95224d318627486f9bc72fd83a1e18cf.doc', 'maker1755652798197_UrEIKt6K.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/95224d318627486f9bc72fd83a1e18cf.doc', '', 100, 1, '2025-08-20 09:20:27.716', 1, '2025-08-20 09:20:27.716', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957977297521311746, '000000', '2025/08/20/abb7095e860a4cc8a128d2d18cd88081.doc', 'maker1755653123666_khuh8rgI.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/abb7095e860a4cc8a128d2d18cd88081.doc', '', 100, 1, '2025-08-20 09:25:39.846', 1, '2025-08-20 09:25:39.846', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957990009915228161, '000000', '2025/08/20/5c5db2c0393b4dd2ba61412f7d087ed8.doc', 'maker1755656162185_CXGng0Ai.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/5c5db2c0393b4dd2ba61412f7d087ed8.doc', '', 100, 1954948962406567938, '2025-08-20 10:16:10.719', 1954948962406567938, '2025-08-20 10:16:10.719', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957990512556425218, '000000', '2025/08/20/f93354f6e7d74e97856f53bf6b97abf5.png', '2025-08-20-10-18-10_90152.png', '.png', 'http://***************:9104/gzyw/2025/08/20/f93354f6e7d74e97856f53bf6b97abf5.png', 'code=oss', 100, 1954948962406567938, '2025-08-20 10:18:10.56', 1954948962406567938, '2025-08-20 10:18:10.56', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957990595096133633, '000000', '2025/08/20/59c3c8924ddd4bc6b5687eb1e9dd5160.png', '2025-08-20-10-18-29_09792.png', '.png', 'http://***************:9104/gzyw/2025/08/20/59c3c8924ddd4bc6b5687eb1e9dd5160.png', 'code=oss', 100, 1954948962406567938, '2025-08-20 10:18:30.242', 1954948962406567938, '2025-08-20 10:18:30.242', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957991195900821506, '000000', '2025/08/20/fc21c5b538504ed7a0af2694a20b40d0.jpg', 'a4d1b13dcab24412ab4a0039f8bc2f53.jpg', '.jpg', 'http://***************:9104/gzyw/2025/08/20/fc21c5b538504ed7a0af2694a20b40d0.jpg', 'code=dsrZp,dsrId=2', 100, 1954948962406567938, '2025-08-20 10:20:53.489', 1954948962406567938, '2025-08-20 10:20:53.489', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957991265069088770, '000000', '2025/08/20/10f59458ce7d4ab995b15eed5f6114aa.doc', 'maker1755656463727_lrv6tf0Y.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/10f59458ce7d4ab995b15eed5f6114aa.doc', '', 100, 1954948962406567938, '2025-08-20 10:21:09.97', 1954948962406567938, '2025-08-20 10:21:09.97', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957991963140046850, '000000', '2025/08/20/80647ad750924f7986e6fdd8449bbe03.doc', 'maker1755656624389_EJdYyJqP.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/80647ad750924f7986e6fdd8449bbe03.doc', '', 100, 1, '2025-08-20 10:23:56.402', 1, '2025-08-20 10:23:56.402', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957992318879580161, '000000', '2025/08/20/052019905c6c4b428c3790671c486315.png', '2025-08-20-10-25-20_20745.png', '.png', 'http://***************:9104/gzyw/2025/08/20/052019905c6c4b428c3790671c486315.png', 'code=oss', 100, 1954948962406567938, '2025-08-20 10:25:21.23', 1954948962406567938, '2025-08-20 10:25:21.23', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957992431320481793, '000000', '2025/08/20/f87f57fd62db495da4338e910cbeca48.png', '2025-08-20-10-25-47_47570.png', '.png', 'http://***************:9104/gzyw/2025/08/20/f87f57fd62db495da4338e910cbeca48.png', 'code=oss', 100, 1954948962406567938, '2025-08-20 10:25:48.031', 1954948962406567938, '2025-08-20 10:25:48.031', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957993377605795841, '000000', '2025/08/20/920095369f5b46808c448e21e5b7e563.doc', 'maker1755656968534_mVgx4Q9J.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/920095369f5b46808c448e21e5b7e563.doc', '', 100, 1954948962406567938, '2025-08-20 10:29:33.65', 1954948962406567938, '2025-08-20 10:29:33.65', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957994081083199490, '000000', '2025/08/20/3440151dc80a4611a6ca02843d4cdbbd.doc', 'maker1755657123606_lZ9OtBiC.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/3440151dc80a4611a6ca02843d4cdbbd.doc', '', 100, 1, '2025-08-20 10:32:21.36', 1, '2025-08-20 10:32:21.36', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1957995993614172162, '000000', '2025/08/20/ba715329019049c68c79f936d98346bd.doc', 'maker1755657589159_dx5ErWZb.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/ba715329019049c68c79f936d98346bd.doc', '', 100, 1954948962406567938, '2025-08-20 10:39:57.352', 1954948962406567938, '2025-08-20 10:39:57.352', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958011367512522754, '000000', '2025/08/20/83e8e5a0c85a4d6c9574390620f23651.docx', '测试空文件.docx', '.docx', 'http://***************:9104/gzyw/2025/08/20/83e8e5a0c85a4d6c9574390620f23651.docx', 'code=oss', NULL, 1, '2025-08-20 11:41:02.765', NULL, '2025-08-20 11:41:02.765', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958057162420940801, '000000', '2025/08/20/03aa1f5a8c504fe7b2cab125645cacb6.doc', 'maker1755672173029_Nrf44x8V.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/03aa1f5a8c504fe7b2cab125645cacb6.doc', '', 100, 1, '2025-08-20 14:43:01.121', 1, '2025-08-20 14:43:01.121', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958061318909095938, '000000', '2025/08/20/5986d145e9884255954b32685ad43251.doc', 'maker1755673162704_13z8wGz3.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/5986d145e9884255954b32685ad43251.doc', '', 100, 1, '2025-08-20 14:59:32.105', 1, '2025-08-20 14:59:32.105', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958081302649581570, '000000', '2025/08/20/c8d64545c8904d89b3e8f9bedc981695.docx', '测试空文件.docx', '.docx', 'http://***************:9104/gzyw/2025/08/20/c8d64545c8904d89b3e8f9bedc981695.docx', 'code=oss', 100, 1, '2025-08-20 16:18:56.6', 1, '2025-08-20 16:18:56.6', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958102003590758402, '000000', '2025/08/20/ae3ba17ed202496fbd106b87c1bc9eab.doc', 'maker1755682862669_VjgWfqbt.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/ae3ba17ed202496fbd106b87c1bc9eab.doc', '', 100, 1, '2025-08-20 17:41:12.089', 1, '2025-08-20 17:41:12.089', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958106185378955266, '000000', '2025/08/20/92ba7a5e8388459dbf112f5e14fe5e6f.doc', 'maker1755683864104_8gPHmuBg.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/92ba7a5e8388459dbf112f5e14fe5e6f.doc', '', NULL, 1, '2025-08-20 17:57:49.105', NULL, '2025-08-20 17:57:49.105', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958106674342526977, '000000', '2025/08/20/13d83185513f431ea282d975111e13fe.doc', 'maker1755683980274_ujxPAkKQ.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/13d83185513f431ea282d975111e13fe.doc', '', 100, 1, '2025-08-20 17:59:45.683', 1, '2025-08-20 17:59:45.683', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958107638113894401, '000000', '2025/08/20/eb64e228cdca442c803e2fc46c452a85.doc', 'maker1755684208271_IcdiCJKh.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/eb64e228cdca442c803e2fc46c452a85.doc', '', 100, 1, '2025-08-20 18:03:35.464', 1, '2025-08-20 18:03:35.464', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958108509287612417, '000000', '2025/08/20/f141638cb4d84942b817e97061ee0497.doc', 'maker1755684417563_oqA4pnCU.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/f141638cb4d84942b817e97061ee0497.doc', '', 100, 1, '2025-08-20 18:07:03.168', 1, '2025-08-20 18:07:03.168', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958154045798809602, '000000', '2025/08/20/2da56742c99e45d692d57affd6c602d5.png', 'Snipaste_2025-06-29_00-10-19.png', '.png', 'http://***************:9104/gzyw/2025/08/20/2da56742c99e45d692d57affd6c602d5.png', 'code=oss', 100, 1, '2025-08-20 21:07:59.918', 1, '2025-08-20 21:07:59.918', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958154238476746754, '000000', '2025/08/20/a74a76037cdc4d5baced96f371ae0f8c.png', 'Snipaste_2025-06-29_00-10-19.png', '.png', 'http://***************:9104/gzyw/2025/08/20/a74a76037cdc4d5baced96f371ae0f8c.png', 'code=oss', 100, 1, '2025-08-20 21:08:45.856', 1, '2025-08-20 21:08:45.856', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958154432220037122, '000000', '2025/08/20/72a684ccaded43d4a19d2377f4fac597.png', 'Snipaste_2025-06-29_00-10-19.png', '.png', 'http://***************:9104/gzyw/2025/08/20/72a684ccaded43d4a19d2377f4fac597.png', 'code=oss', 100, 1, '2025-08-20 21:09:32.048', 1, '2025-08-20 21:09:32.048', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958155463993655298, '000000', '2025/08/20/61f20d2608554857981d833ad2a501ce.png', 'Snipaste_2025-06-29_00-10-19.png', '.png', 'http://***************:9104/gzyw/2025/08/20/61f20d2608554857981d833ad2a501ce.png', 'code=oss', 100, 1, '2025-08-20 21:13:38.042', 1, '2025-08-20 21:13:38.042', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958167352826789889, '000000', '2025/08/20/3f39945f7af249239b04e5a65b6f98f6.doc', 'maker1755698445991_EG4DjYcq.doc', '.doc', 'http://***************:9104/gzyw/2025/08/20/3f39945f7af249239b04e5a65b6f98f6.doc', '', 100, 1, '2025-08-20 22:00:52.561', 1, '2025-08-20 22:00:52.561', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958516008986181634, '000000', '2025/08/21/5b5ed56275e042c886ac97d06d932eda.jpg', 'dd010788c6264d249c3ae60b5589a4a4.jpg', '.jpg', 'http://***************:9104/gzyw/2025/08/21/5b5ed56275e042c886ac97d06d932eda.jpg', 'code=dsrZp,dsrId=1', 100, 1, '2025-08-21 21:06:18.67', 1, '2025-08-21 21:06:18.67', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958517068396085250, '000000', '2025/08/21/09107a1d39d642b18caf92af7541bdcb.doc', 'maker1755781814513_c7MO5o46.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/09107a1d39d642b18caf92af7541bdcb.doc', '', 100, 1, '2025-08-21 21:10:31.252', 1, '2025-08-21 21:10:31.252', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958517467123400706, '000000', '2025/08/21/a8e03bd57f764ffab9e33ccd5117b168.doc', 'maker1755781916531_6AZjeMSs.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/a8e03bd57f764ffab9e33ccd5117b168.doc', '', 100, 1, '2025-08-21 21:12:06.319', 1, '2025-08-21 21:12:06.319', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958519093129920513, '000000', '2025/08/21/a291968c627d43dca12f67ff895d7c7f.doc', 'maker1755782303805_JSdgo1C0.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/a291968c627d43dca12f67ff895d7c7f.doc', '', 100, 1, '2025-08-21 21:18:33.986', 1, '2025-08-21 21:18:33.986', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958520041247457282, '000000', '2025/08/21/6c720fe5338a4adfb53f05428af07d32.doc', 'ff8080816b098258016b0d10c2774e49.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/6c720fe5338a4adfb53f05428af07d32.doc', 'code=oss', 100, 1, '2025-08-21 21:22:20.04', 1, '2025-08-21 21:22:20.04', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958520194335358978, '000000', '2025/08/21/5bc40eefd61f4bef8fdaabaae11de67a.doc', 'maker1755782566481_8FeeUmBX.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/5bc40eefd61f4bef8fdaabaae11de67a.doc', '', 100, 1, '2025-08-21 21:22:56.536', 1, '2025-08-21 21:22:56.536', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958520601468059649, '000000', '2025/08/21/c6ee33e7e51e472280ef2936b2465e72.png', '2025-08-21-21-24-33_73011.png', '.png', 'http://***************:9104/gzyw/2025/08/21/c6ee33e7e51e472280ef2936b2465e72.png', 'code=oss', 100, 1, '2025-08-21 21:24:33.602', 1, '2025-08-21 21:24:33.602', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958520642115059714, '000000', '2025/08/21/006292a8725847069895f08f54ec1e5c.png', '2025-08-21-21-24-42_82667.png', '.png', 'http://***************:9104/gzyw/2025/08/21/006292a8725847069895f08f54ec1e5c.png', 'code=oss', 100, 1, '2025-08-21 21:24:43.295', 1, '2025-08-21 21:24:43.295', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958520932218290178, '000000', '2025/08/21/be132db23c7f425d9edd6e11d2de85e1.doc', 'maker1755782742386_ohhA8E9s.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/be132db23c7f425d9edd6e11d2de85e1.doc', '', 100, 1, '2025-08-21 21:25:52.471', 1, '2025-08-21 21:25:52.471', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958521707225006081, '000000', '2025/08/21/ba75d9db87b34bcdae09fc1050e13c7d.doc', 'maker1755782913901_uIkV3PUx.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/ba75d9db87b34bcdae09fc1050e13c7d.doc', '', 100, 1, '2025-08-21 21:28:57.241', 1, '2025-08-21 21:28:57.241', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958531848280285186, '000000', '2025/08/21/43b487fe326b4be584c2478c9518763d.doc', 'maker1755785326967_oQDPDQL4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/43b487fe326b4be584c2478c9518763d.doc', '', 100, 1, '2025-08-21 22:09:15.052', 1, '2025-08-21 22:09:15.052', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958532101503000577, '000000', '2025/08/21/923ac9be7c00464d80d3153a8cfca717.doc', 'maker1755785402049_YFWMKnzZ.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/923ac9be7c00464d80d3153a8cfca717.doc', '', 100, 1, '2025-08-21 22:10:15.432', 1, '2025-08-21 22:10:15.432', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958537250258640897, '000000', '2025/08/21/8c36640a6b0142549c29edbffadc41b4.doc', 'maker1755786602002_l7nSbswd.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/8c36640a6b0142549c29edbffadc41b4.doc', '', 100, 1, '2025-08-21 22:30:42.993', 1, '2025-08-21 22:30:42.993', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958542096307167233, '000000', '2025/08/21/012ba6d83902475688a07022c935de20.doc', 'maker1755787759277_sJgdUDsg.doc', '.doc', 'http://***************:9104/gzyw/2025/08/21/012ba6d83902475688a07022c935de20.doc', '', 100, 1, '2025-08-21 22:49:58.372', 1, '2025-08-21 22:49:58.372', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958698417316249602, '000000', '2025/08/22/b8b3a82c2118407cac59dbaadadb32f6.doc', 'maker1755825053010_ZLszXBlT.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/b8b3a82c2118407cac59dbaadadb32f6.doc', '', 100, 1, '2025-08-22 09:11:08.205', 1, '2025-08-22 09:11:08.205', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958701738265493506, '000000', '2025/08/22/75860b17fa264c568fe195c0f2db104a.doc', 'maker1755825836882_fG9txz1D.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/75860b17fa264c568fe195c0f2db104a.doc', '', 100, 1, '2025-08-22 09:24:19.986', 1, '2025-08-22 09:24:19.986', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958806159511605249, '000000', '2025/08/22/45d5136db033433e99299a73cb573677.doc', 'ff80808166eef1020166f2af724721db.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/45d5136db033433e99299a73cb573677.doc', 'code=oss', 100, 1, '2025-08-22 16:19:15.945', 1, '2025-08-22 16:19:15.945', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958806427984809986, '000000', '2025/08/22/7ff9fff0d5724d7c8e2124072d908ce9.doc', '公证企业申请表.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/7ff9fff0d5724d7c8e2124072d908ce9.doc', 'code=oss', 100, 1, '2025-08-22 16:20:19.953', 1, '2025-08-22 16:20:19.953', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958806488718331905, '000000', '2025/08/22/19df5897056f4100ae2b0cc76453a09e.doc', '公证自然人申请表.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/19df5897056f4100ae2b0cc76453a09e.doc', 'code=oss', 100, 1, '2025-08-22 16:20:34.443', 1, '2025-08-22 16:20:34.443', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958807062704640001, '000000', '2025/08/22/0db8bc07296a4643a14ace247e8d9198.doc', 'maker1755850958963_8oXMJEuI.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/0db8bc07296a4643a14ace247e8d9198.doc', '', 100, 1, '2025-08-22 16:22:51.295', 1, '2025-08-22 16:22:51.295', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958810269774249986, '000000', '2025/08/22/f73c186e3e1848baa97c3edbdbe32018.doc', '公证自然人申请表.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/f73c186e3e1848baa97c3edbdbe32018.doc', 'code=oss', 100, 1, '2025-08-22 16:35:35.906', 1, '2025-08-22 16:35:35.906', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958810589766090753, '000000', '2025/08/22/50991377b28746b995649742ab0c15ee.doc', 'maker1755851799464_w69jqxz5.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/50991377b28746b995649742ab0c15ee.doc', '', 100, 1, '2025-08-22 16:36:52.204', 1, '2025-08-22 16:36:52.204', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958813188032655362, '000000', '2025/08/22/2deb0c54fe514be3ba4ffdbf827842e1.doc', 'maker1755852394498_go7EWhLU.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/2deb0c54fe514be3ba4ffdbf827842e1.doc', '', 100, 1, '2025-08-22 16:47:11.675', 1, '2025-08-22 16:47:11.675', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958813775981801474, '000000', '2025/08/22/03123b335fef4e5b8448fb95fbb743c0.doc', 'maker1755852562526_UnYCvOfo.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/03123b335fef4e5b8448fb95fbb743c0.doc', '', 100, 1, '2025-08-22 16:49:31.855', 1, '2025-08-22 16:49:31.855', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958813988029034497, '000000', '2025/08/22/aeafa3f7ea304a89a831a29590cbc532.doc', 'maker1755852613502_QSg6gmMH.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/aeafa3f7ea304a89a831a29590cbc532.doc', '', 100, 1, '2025-08-22 16:50:22.417', 1, '2025-08-22 16:50:22.417', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958814267822665730, '000000', '2025/08/22/e02b8f2242cd4de2b91c18c3e439b7f2.doc', 'maker1755852680098_yzVDy3Xz.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/e02b8f2242cd4de2b91c18c3e439b7f2.doc', '', 100, 1, '2025-08-22 16:51:29.128', 1, '2025-08-22 16:51:29.128', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958814942686179330, '000000', '2025/08/22/58fddb46d5aa45b7a19f5b07e4f3f4b0.doc', 'maker1755852839573_LkM6EhIG.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/58fddb46d5aa45b7a19f5b07e4f3f4b0.doc', '', 100, 1, '2025-08-22 16:54:10.028', 1, '2025-08-22 16:54:10.028', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958815127856312322, '000000', '2025/08/22/084be420ec6548d4a1d92c214629aa5f.doc', 'maker1755852867006_LHMJkw8g.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/084be420ec6548d4a1d92c214629aa5f.doc', '', 100, 1, '2025-08-22 16:54:54.167', 1, '2025-08-22 16:54:54.167', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958815553741746177, '000000', '2025/08/22/2d757b8732ef4774bbd4af074135e392.doc', 'maker1755852960572_WePU8Aq6.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/2d757b8732ef4774bbd4af074135e392.doc', '', 100, 1, '2025-08-22 16:56:35.705', 1, '2025-08-22 16:56:35.705', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958817256285179905, '000000', '2025/08/22/eaf619733c064e8db3b648fcb3d3cf08.doc', 'maker1755853381526_cJMG91xF.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/eaf619733c064e8db3b648fcb3d3cf08.doc', '', 100, 1, '2025-08-22 17:03:21.623', 1, '2025-08-22 17:03:21.623', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958817664273518594, '000000', '2025/08/22/8d97a1719f3b45d7942f0b9a0d3998e9.doc', 'maker1755853471446_KtFmYj8j.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/8d97a1719f3b45d7942f0b9a0d3998e9.doc', '', 100, 1, '2025-08-22 17:04:58.905', 1, '2025-08-22 17:04:58.905', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958819136335794177, '000000', '2025/08/22/5aca2ec24c6d43cca4bceabc8b8fbf79.doc', 'maker1755853833080_IZZKLfQm.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/5aca2ec24c6d43cca4bceabc8b8fbf79.doc', '', 100, 1, '2025-08-22 17:10:49.861', 1, '2025-08-22 17:10:49.861', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958821117838323713, '000000', '2025/08/22/de39ec6c34fd475e989b1084bc6001be.doc', '公证自然人申请表.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/de39ec6c34fd475e989b1084bc6001be.doc', 'code=oss', 100, 1, '2025-08-22 17:18:42.287', 1, '2025-08-22 17:18:42.287', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958821283911790593, '000000', '2025/08/22/7d51cfa1bacd49b6ac9bf8dbcba6f302.doc', 'maker1755854349183_w5wAQpo8.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/7d51cfa1bacd49b6ac9bf8dbcba6f302.doc', '', 100, 1, '2025-08-22 17:19:21.882', 1, '2025-08-22 17:19:21.882', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958824007785111553, '000000', '2025/08/22/54fa40289a244d3c8730288c43d6ef2c.doc', 'maker1755854996198_K1wb1px6.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/54fa40289a244d3c8730288c43d6ef2c.doc', '', 100, 1, '2025-08-22 17:30:11.307', 1, '2025-08-22 17:30:11.307', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958855409570275330, '000000', '2025/08/22/ba38d61769c14e20b4f29bd369b0218b.doc', 'maker1755862474827_G0WqTZfx.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/ba38d61769c14e20b4f29bd369b0218b.doc', '', 100, 1, '2025-08-22 19:34:58.075', 1, '2025-08-22 19:34:58.075', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958856652875169794, '000000', '2025/08/22/0790f1b323e44187a95196e2390fee0e.doc', 'maker1755862774105_gQQyBIpn.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/0790f1b323e44187a95196e2390fee0e.doc', '', 100, 1, '2025-08-22 19:39:54.502', 1, '2025-08-22 19:39:54.502', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958858073456517121, '000000', '2025/08/22/6e8a63f98ba343868b6aa0e70fc9f39d.doc', 'maker1755863112945_0ZR1AlVa.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/6e8a63f98ba343868b6aa0e70fc9f39d.doc', '', 100, 1, '2025-08-22 19:45:33.203', 1, '2025-08-22 19:45:33.203', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958863540962467841, '000000', '2025/08/22/59e26dcf25ba4e898427e0dbc2586138.doc', 'maker1755864420706_B6X6jjJV.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/59e26dcf25ba4e898427e0dbc2586138.doc', '', 100, 1, '2025-08-22 20:07:16.751', 1, '2025-08-22 20:07:16.751', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958873060681981954, '000000', '2025/08/22/af3edcb729da448ebe64ed5e5c89cc02.doc', 'maker1755866689357_WbyvDlv2.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/af3edcb729da448ebe64ed5e5c89cc02.doc', '', 100, 1, '2025-08-22 20:45:06.433', 1, '2025-08-22 20:45:06.433', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958878665400512514, '000000', '2025/08/22/3d76732f7a9142ef86576661a0633906.doc', 'maker1755868029671_ArKf16dQ.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/3d76732f7a9142ef86576661a0633906.doc', '', 100, 1, '2025-08-22 21:07:22.695', 1, '2025-08-22 21:07:22.695', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958884130452746242, '000000', '2025/08/22/0af20d5bb6ee4526b64639fc76ac9313.doc', 'maker1755869327989_y7ihAdfu.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/0af20d5bb6ee4526b64639fc76ac9313.doc', '', 100, 1, '2025-08-22 21:29:05.665', 1, '2025-08-22 21:29:05.665', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958885660350222338, '000000', '2025/08/22/3a11607675864948881dc95d3e76eedd.doc', 'maker1755869699697_8XKeHtMb.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/3a11607675864948881dc95d3e76eedd.doc', '', 100, 1, '2025-08-22 21:35:10.43', 1, '2025-08-22 21:35:10.43', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958887680939466753, '000000', '2025/08/22/518d0f3b55c24bcda156718c4a0861cf.doc', 'maker1755870181317_oqdOgXRV.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/518d0f3b55c24bcda156718c4a0861cf.doc', '', 100, 1, '2025-08-22 21:43:12.175', 1, '2025-08-22 21:43:12.175', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958889150992633857, '000000', '2025/08/22/4eff7d56e8d74a8e88ab2b410f1b90c6.doc', 'maker1755870532185_UiIccNvb.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/4eff7d56e8d74a8e88ab2b410f1b90c6.doc', '', 100, 1, '2025-08-22 21:49:02.661', 1, '2025-08-22 21:49:02.661', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958889884576403457, '000000', '2025/08/22/e5aa9d5300cd45a98680b022b7284c77.doc', '个人信息表.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/e5aa9d5300cd45a98680b022b7284c77.doc', 'code=oss', 100, 1, '2025-08-22 21:51:57.563', 1, '2025-08-22 21:51:57.563', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958890025534377986, '000000', '2025/08/22/75ccdb514be74f00bb38092158063c91.doc', 'maker1755870744670_j6lnBEsu.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/75ccdb514be74f00bb38092158063c91.doc', '', 100, 1, '2025-08-22 21:52:31.164', 1, '2025-08-22 21:52:31.164', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958892856086450177, '000000', '2025/08/22/00d40f8005cf4166911b3bbb30ed2804.doc', 'maker1755871411578_MwgHeUnW.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/00d40f8005cf4166911b3bbb30ed2804.doc', '', 100, 1, '2025-08-22 22:03:46.018', 1, '2025-08-22 22:03:46.018', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958893833225064449, '000000', '2025/08/22/40d0a2ef736a4cf4b6ab17d2d98ab0a7.doc', 'maker1755871652749_Li8UXve9.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/40d0a2ef736a4cf4b6ab17d2d98ab0a7.doc', '', 100, 1, '2025-08-22 22:07:38.985', 1, '2025-08-22 22:07:38.985', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958895435855187970, '000000', '2025/08/22/853db05404554c6a874ea81f0969c816.doc', 'maker1755872028790_hO5jl603.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/853db05404554c6a874ea81f0969c816.doc', '', 100, 1, '2025-08-22 22:14:01.082', 1, '2025-08-22 22:14:01.082', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958896055857205249, '000000', '2025/08/22/9bcd02848a524296ae93470ee2a43859.doc', '个人信息表.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/9bcd02848a524296ae93470ee2a43859.doc', 'code=oss', 100, 1, '2025-08-22 22:16:28.91', 1, '2025-08-22 22:16:28.91', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958896326284955649, '000000', '2025/08/22/63fc51c2a3cd40cbb00b6ed5d570d922.doc', 'maker1755872247147_p3CZm8mX.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/63fc51c2a3cd40cbb00b6ed5d570d922.doc', '', 100, 1, '2025-08-22 22:17:33.39', 1, '2025-08-22 22:17:33.39', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958902810788077570, '000000', '2025/08/22/c580a0f30d0f48b39eb2438759cb0d39.doc', 'maker1755873778090_VLNhHgGE.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/c580a0f30d0f48b39eb2438759cb0d39.doc', '', 100, 1, '2025-08-22 22:43:19.411', 1, '2025-08-22 22:43:19.411', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958903224262565889, '000000', '2025/08/22/b00d84149abd46af90e7270079ef8e03.doc', 'maker1755873879097_4JpKA5sM.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/b00d84149abd46af90e7270079ef8e03.doc', '', 100, 1, '2025-08-22 22:44:57.993', 1, '2025-08-22 22:44:57.993', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958903431209525249, '000000', '2025/08/22/3993ef225f5a49a68d95db98e8496337.doc', 'maker1755873938091_9DUdmZiE.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/3993ef225f5a49a68d95db98e8496337.doc', '', 100, 1, '2025-08-22 22:45:47.326', 1, '2025-08-22 22:45:47.326', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958908252348055554, '000000', '2025/08/22/f89a245dc8a3422993fd6aa118dbf2e8.doc', 'maker1755875085331_xZbn8NCy.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/f89a245dc8a3422993fd6aa118dbf2e8.doc', '', 100, 1, '2025-08-22 23:04:56.774', 1, '2025-08-22 23:04:56.774', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958908572105015298, '000000', '2025/08/22/6356f87c60d54071a7fbd201d840ddc1.doc', 'maker1755875165467_TtMPD9xu.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/6356f87c60d54071a7fbd201d840ddc1.doc', '', 100, 1, '2025-08-22 23:06:13.013', 1, '2025-08-22 23:06:13.013', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958910339702173698, '000000', '2025/08/22/822590f5aa454cb3875e3d114b490f16.doc', 'maker1755875580966_e5OlbL8C.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/822590f5aa454cb3875e3d114b490f16.doc', '', 100, 1, '2025-08-22 23:13:14.439', 1, '2025-08-22 23:13:14.439', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958910735283761154, '000000', '2025/08/22/9cb8223ae0c34ae2a13ef569e5d18fbb.doc', 'maker1755875679247_eRGuB7C4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/9cb8223ae0c34ae2a13ef569e5d18fbb.doc', '', 100, 1, '2025-08-22 23:14:48.761', 1, '2025-08-22 23:14:48.761', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958911973270319106, '000000', '2025/08/22/2b22c93b048d4d0da783b9335388ae26.doc', 'maker1755875970727_XRVmuCav.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/2b22c93b048d4d0da783b9335388ae26.doc', '', 100, 1, '2025-08-22 23:19:43.92', 1, '2025-08-22 23:19:43.92', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958913374587199490, '000000', '2025/08/22/db9138bb3f334dc084763344fb813d85.doc', 'maker1755876305504_1yxRF8ga.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/db9138bb3f334dc084763344fb813d85.doc', '', 100, 1, '2025-08-22 23:25:18.016', 1, '2025-08-22 23:25:18.016', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958915729609195522, '000000', '2025/08/22/7bd853c126934d9c97e5a7ae549ceeaa.doc', 'maker1755876865379_SQxPFxfy.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/7bd853c126934d9c97e5a7ae549ceeaa.doc', '', 100, 1, '2025-08-22 23:34:39.492', 1, '2025-08-22 23:34:39.492', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958917244512206850, '000000', '2025/08/22/b17954da16144b2c85a38aef0feda1b0.doc', 'maker1755877229285_DxU6mag4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/b17954da16144b2c85a38aef0feda1b0.doc', '', 100, 1, '2025-08-22 23:40:40.676', 1, '2025-08-22 23:40:40.676', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958919901817675777, '000000', '2025/08/22/ed3d8438dc224137bb2f04bfa8ab6dd8.doc', 'maker1755877857570_tJtF1VhC.doc', '.doc', 'http://***************:9104/gzyw/2025/08/22/ed3d8438dc224137bb2f04bfa8ab6dd8.doc', '', 100, 1, '2025-08-22 23:51:14.224', 1, '2025-08-22 23:51:14.224', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958922821640409090, '000000', '2025/08/23/0a2f4029a2994adca5728b8795640491.doc', 'maker1755878556776_2g4H1Gr0.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/0a2f4029a2994adca5728b8795640491.doc', '', 100, 1, '2025-08-23 00:02:50.366', 1, '2025-08-23 00:02:50.366', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958924935276113922, '000000', '2025/08/23/178880dab9f045a5beafc59c171c26a5.doc', 'maker1755879060722_lyhE4j67.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/178880dab9f045a5beafc59c171c26a5.doc', '', 100, 1, '2025-08-23 00:11:14.294', 1, '2025-08-23 00:11:14.294', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958925192588275714, '000000', '2025/08/23/b5843f287cc24ac1b10035bea1ec1acb.doc', 'maker1755879107780_V3bhErr8.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/b5843f287cc24ac1b10035bea1ec1acb.doc', '', 100, 1, '2025-08-23 00:12:15.648', 1, '2025-08-23 00:12:15.648', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958926078156734466, '000000', '2025/08/23/36511677a35147d68c52e25cef0f6f52.doc', 'maker1755879327490_lF4tQo40.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/36511677a35147d68c52e25cef0f6f52.doc', '', 100, 1, '2025-08-23 00:15:46.785', 1, '2025-08-23 00:15:46.785', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958926439181451266, '000000', '2025/08/23/da231dea56924d7b93d2e4fb7cf8c4ad.doc', 'maker1755879419257_pauOr1cV.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/da231dea56924d7b93d2e4fb7cf8c4ad.doc', '', 100, 1, '2025-08-23 00:17:12.852', 1, '2025-08-23 00:17:12.852', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958931551123750914, '000000', '2025/08/23/da900eb81015442d941ba95fec676213.doc', 'maker1755880620614_FUSvlq7K.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/da900eb81015442d941ba95fec676213.doc', '', 100, 1, '2025-08-23 00:37:31.635', 1, '2025-08-23 00:37:31.635', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958931726001061890, '000000', '2025/08/23/162fc4b8d3ba4358905da4bec7e066c4.doc', 'maker1755880683074_ROwFaI3e.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/162fc4b8d3ba4358905da4bec7e066c4.doc', '', 100, 1, '2025-08-23 00:38:13.333', 1, '2025-08-23 00:38:13.333', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958932001004797954, '000000', '2025/08/23/b831397eb34b40ad86d791dac2f664e2.doc', 'maker1755880751211_APArsGw1.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/b831397eb34b40ad86d791dac2f664e2.doc', '', 100, 1, '2025-08-23 00:39:18.895', 1, '2025-08-23 00:39:18.895', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958932285605101570, '000000', '2025/08/23/d83f6c7c5b554fd9b5bd5c7d6216af6f.doc', 'maker1755880801269_1QCPwBFk.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/d83f6c7c5b554fd9b5bd5c7d6216af6f.doc', '', 100, 1, '2025-08-23 00:40:26.748', 1, '2025-08-23 00:40:26.748', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958932781313032194, '000000', '2025/08/23/633654de77ff4088a1a579db978226d0.doc', 'maker1755880933590_30i6mcGT.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/633654de77ff4088a1a579db978226d0.doc', '', 100, 1, '2025-08-23 00:42:24.94', 1, '2025-08-23 00:42:24.94', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1958932995184787457, '000000', '2025/08/23/89932f165c3544e29b23743da083c34a.doc', 'maker1755880987790_730YSPQF.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/89932f165c3544e29b23743da083c34a.doc', '', 100, 1, '2025-08-23 00:43:15.933', 1, '2025-08-23 00:43:15.933', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959187381328904193, '000000', '2025/08/23/be5aab552ee44a28a3bdf8fb8d4a1935.pdf', 'maker1755941607256_XU7K01MV.pdf', '.pdf', 'http://***************:9104/gzyw/2025/08/23/be5aab552ee44a28a3bdf8fb8d4a1935.pdf', '', 100, 1, '2025-08-23 17:34:06.31', 1, '2025-08-23 17:34:06.31', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959193765453795329, '000000', '2025/08/23/7eaddf56d89d4de2aee7aa3feacd37b0.doc', 'maker1755943145873_ETGZgcHS.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/7eaddf56d89d4de2aee7aa3feacd37b0.doc', '', 100, 1, '2025-08-23 17:59:28.405', 1, '2025-08-23 17:59:28.405', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959197705880743937, '000000', '2025/08/23/5609015fba634c7cb06cae4e3d1ccfc4.doc', 'maker1755944085146_0NaYl0bb.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/5609015fba634c7cb06cae4e3d1ccfc4.doc', '', 100, 1, '2025-08-23 18:15:07.875', 1, '2025-08-23 18:15:07.875', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959200204553531393, '000000', '2025/08/23/7c5bd8c37fb54b99a7eaecfcb0c4a24b.doc', 'maker1755944686293_zSVdnuSy.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/7c5bd8c37fb54b99a7eaecfcb0c4a24b.doc', '', 100, 1, '2025-08-23 18:25:03.604', 1, '2025-08-23 18:25:03.604', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959200350360121345, '000000', '2025/08/23/aac162b43c024b8ab9be83b8c25ee271.doc', 'maker1755944730491_DQ6FpiV5.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/aac162b43c024b8ab9be83b8c25ee271.doc', '', 100, 1, '2025-08-23 18:25:38.37', 1, '2025-08-23 18:25:38.37', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959200567805423617, '000000', '2025/08/23/19b6902dd3e74f3f85967ceebe636914.doc', 'maker1755944781491_3bqtHWhH.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/19b6902dd3e74f3f85967ceebe636914.doc', '', 100, 1, '2025-08-23 18:26:30.216', 1, '2025-08-23 18:26:30.216', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959206972583133185, '000000', '2025/08/23/317329e9b6924eba83fc3761b5d187d1.doc', 'maker1755946277086_hWH1GwOE.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/317329e9b6924eba83fc3761b5d187d1.doc', '', 100, 1, '2025-08-23 18:51:57.227', 1, '2025-08-23 18:51:57.227', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959208756219322370, '000000', '2025/08/23/256d98b650c54446afbfae6e7785eed5.doc', 'maker1755946730293_cBJZNrrc.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/256d98b650c54446afbfae6e7785eed5.doc', '', 100, 1, '2025-08-23 18:59:02.479', 1, '2025-08-23 18:59:02.479', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959209514922778626, '000000', '2025/08/23/3745bae07d38434092358a1e6f852ea5.doc', 'maker1755946901767_6TZ9W6dr.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/3745bae07d38434092358a1e6f852ea5.doc', '', 100, 1, '2025-08-23 19:02:03.38', 1, '2025-08-23 19:02:03.38', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959209913369075713, '000000', '2025/08/23/33d37529a36346acb32ae99cda7aed18.doc', 'maker1755947011308_HgVkCQIt.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/33d37529a36346acb32ae99cda7aed18.doc', '', 100, 1, '2025-08-23 19:03:38.368', 1, '2025-08-23 19:03:38.368', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959210317515431937, '000000', '2025/08/23/df9a0b8b56cd4d76a08cff4c5f000ee0.doc', 'maker1755947084497_TW2ZlrPy.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/df9a0b8b56cd4d76a08cff4c5f000ee0.doc', '', 100, 1, '2025-08-23 19:05:14.729', 1, '2025-08-23 19:05:14.729', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959213100339658754, '000000', '2025/08/23/82076a4a85a5409ab98d9e51d41a5391.doc', 'maker1755947756562_83mHtTgL.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/82076a4a85a5409ab98d9e51d41a5391.doc', '', 100, 1, '2025-08-23 19:16:18.198', 1, '2025-08-23 19:16:18.198', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959215039114739714, '000000', '2025/08/23/cc7e5baf9a8d49b4a762ad87d8fc4ddc.doc', 'maker1755948213252_fHceoFPp.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/cc7e5baf9a8d49b4a762ad87d8fc4ddc.doc', '', 100, 1, '2025-08-23 19:24:00.446', 1, '2025-08-23 19:24:00.446', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959216008284094465, '000000', '2025/08/23/67ac43fca0e84d169cd26376a7595d49.doc', 'maker1755948458064_DPW1fNPG.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/67ac43fca0e84d169cd26376a7595d49.doc', '', 100, 1, '2025-08-23 19:27:51.509', 1, '2025-08-23 19:27:51.509', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959216391333101569, '000000', '2025/08/23/4a43bada3290416ea80911110bfd31db.doc', '公证企业申请表 (1).doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/4a43bada3290416ea80911110bfd31db.doc', 'code=oss', 100, 1, '2025-08-23 19:29:22.838', 1, '2025-08-23 19:29:22.838', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959218430993776642, '000000', '2025/08/23/d422dde5338d4f0f984cfd22665b6f18.doc', 'maker1755949039070_aSdToUL7.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/d422dde5338d4f0f984cfd22665b6f18.doc', '', 100, 1, '2025-08-23 19:37:29.135', 1, '2025-08-23 19:37:29.135', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959265908364197889, '000000', '2025/08/23/6f289f070a7e4da2a32de84b50759f67.doc', 'maker1755960354947_YZqVR9gf.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/6f289f070a7e4da2a32de84b50759f67.doc', '', 100, 1, '2025-08-23 22:46:08.611', 1, '2025-08-23 22:46:08.611', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959267965842276353, '000000', '2025/08/23/16f42bc898f04dc6ab0e5a859c2c2444.doc', 'maker1755960852485_2eVbjlUO.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/16f42bc898f04dc6ab0e5a859c2c2444.doc', '', 100, 1, '2025-08-23 22:54:19.153', 1, '2025-08-23 22:54:19.153', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959268058234404866, '000000', '2025/08/23/9d10f6a1c4554c38b8ea745d51565c18.doc', 'maker1755960877065_WZ9MfXXs.doc', '.doc', 'http://***************:9104/gzyw/2025/08/23/9d10f6a1c4554c38b8ea745d51565c18.doc', '', 100, 1, '2025-08-23 22:54:41.18', 1, '2025-08-23 22:54:41.18', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959525409763971074, '000000', '2025/08/24/7273e8b718c2405b819dfebc388c7274.doc', 'maker1756022231631_ujsrN5cx.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/7273e8b718c2405b819dfebc388c7274.doc', '', 100, 1, '2025-08-24 15:57:18.566', 1, '2025-08-24 15:57:18.566', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959526022581145602, '000000', '2025/08/24/a1de8602965b412ba327bea7ebd70df1.doc', 'maker1756022381281_pvFjT3Ls.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/a1de8602965b412ba327bea7ebd70df1.doc', '', 100, 1, '2025-08-24 15:59:44.673', 1, '2025-08-24 15:59:44.673', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959526298851561474, '000000', '2025/08/24/e7bfdff4e7ba488eaa9463b3d7af4f72.doc', 'maker1756022446990_TgqIDJOp.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/e7bfdff4e7ba488eaa9463b3d7af4f72.doc', '', 100, 1, '2025-08-24 16:00:50.541', 1, '2025-08-24 16:00:50.541', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959527412628017154, '000000', '2025/08/24/9e7f1aadc741470cbc9fdcd3611f268d.doc', 'maker1756022710064_nKQI4Yg4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/9e7f1aadc741470cbc9fdcd3611f268d.doc', '', 100, 1, '2025-08-24 16:05:16.086', 1, '2025-08-24 16:05:16.086', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959529617921757185, '000000', '2025/08/24/53dcb379a6104c7f88f9d214402105f9.doc', 'maker1756023236441_WrleJ1Vt.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/53dcb379a6104c7f88f9d214402105f9.doc', '', 100, 1, '2025-08-24 16:14:01.869', 1, '2025-08-24 16:14:01.869', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959542137386520577, '000000', '2025/08/24/7aebec5e499448a4894168f5aeca623b.docx', '系统截图-系统和财务等.docx', '.docx', 'http://***************:9104/gzyw/2025/08/24/7aebec5e499448a4894168f5aeca623b.docx', 'code=oss', 100, 1, '2025-08-24 17:03:46.743', 1, '2025-08-24 17:03:46.743', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959542502609735682, '000000', '2025/08/24/fa585d40c11640c5bc88de6cf3f1c1f1.docx', '测试文档1.docx', '.docx', 'http://***************:9104/gzyw/2025/08/24/fa585d40c11640c5bc88de6cf3f1c1f1.docx', 'code=oss', 100, 1, '2025-08-24 17:05:13.819', 1, '2025-08-24 17:05:13.819', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959543772045201409, '000000', '2025/08/24/0de71fde0d0640d7a9be66ad92fc72f0.doc', 'maker1756026611152_9w0ScgM4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/0de71fde0d0640d7a9be66ad92fc72f0.doc', '', 100, 1, '2025-08-24 17:10:16.475', 1, '2025-08-24 17:10:16.475', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959543877263511553, '000000', '2025/08/24/1954cd3839754875934b9f52091f0d05.doc', 'maker1756026638364_l2CVzwkb.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/1954cd3839754875934b9f52091f0d05.doc', '', 100, 1, '2025-08-24 17:10:41.562', 1, '2025-08-24 17:10:41.562', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959543967336189954, '000000', '2025/08/24/feca55dbb3cd43969d116a8137f2b67b.doc', 'maker1756026659796_iSrev7FN.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/feca55dbb3cd43969d116a8137f2b67b.doc', '', 100, 1, '2025-08-24 17:11:03.036', 1, '2025-08-24 17:11:03.036', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959544103982419970, '000000', '2025/08/24/27e66dffc8ab4cada117c124f61dd194.doc', 'maker1756026692401_xTvjvuQz.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/27e66dffc8ab4cada117c124f61dd194.doc', '', 100, 1, '2025-08-24 17:11:35.617', 1, '2025-08-24 17:11:35.617', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959544541070839810, '000000', '2025/08/24/79051d861c0c44f58a29588b3bd8a26d.doc', 'maker1756026796530_qlEc07vT.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/79051d861c0c44f58a29588b3bd8a26d.doc', '', 100, 1, '2025-08-24 17:13:19.825', 1, '2025-08-24 17:13:19.825', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959545052738179074, '000000', '2025/08/24/a21d6cf7286e43c9b0fece4df3259155.doc', 'maker1756026918393_UCoudyTR.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/a21d6cf7286e43c9b0fece4df3259155.doc', '', 100, 1, '2025-08-24 17:15:21.816', 1, '2025-08-24 17:15:21.816', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959545391231094785, '000000', '2025/08/24/0408a053dc484d3cbc908878d40504a2.doc', 'maker1756026999259_pqo45hGf.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/0408a053dc484d3cbc908878d40504a2.doc', '', 100, 1, '2025-08-24 17:16:42.519', 1, '2025-08-24 17:16:42.519', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959545429864828929, '000000', '2025/08/24/833cd50fe0214fbbb30991668dfd2115.doc', 'maker1756027008443_v47H5Uz9.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/833cd50fe0214fbbb30991668dfd2115.doc', '', 100, 1, '2025-08-24 17:16:51.731', 1, '2025-08-24 17:16:51.731', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959545514560409602, '000000', '2025/08/24/a60d7db09b3b407a904c175a4288dfa9.doc', 'maker1756027028621_2gqfoM54.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/a60d7db09b3b407a904c175a4288dfa9.doc', '', 100, 1, '2025-08-24 17:17:11.923', 1, '2025-08-24 17:17:11.923', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959545556994183169, '000000', '2025/08/24/7a9db9853c3743c0bd8f75578b7bfe0c.doc', 'maker1756027038788_zfuUKCZS.doc', '.doc', 'http://***************:9104/gzyw/2025/08/24/7a9db9853c3743c0bd8f75578b7bfe0c.doc', '', 100, 1, '2025-08-24 17:17:22.041', 1, '2025-08-24 17:17:22.041', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959806498435616769, '000000', '2025/08/25/0fba3e65c5ac469d93ea05f3160769f8.docx', 'GZ业务H5截图（爱南宁）.docx', '.docx', 'http://***************:9104/gzyw/2025/08/25/0fba3e65c5ac469d93ea05f3160769f8.docx', 'code=oss', 100, 1, '2025-08-25 10:34:15.328', 1, '2025-08-25 10:34:15.328', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959909419303161857, '000000', '2025/08/25/4a64f1b459854287bc7ed721cebe2c6d.png', '2025-08-25-17-23-12_92714.png', '.png', 'http://***************:9104/gzyw/2025/08/25/4a64f1b459854287bc7ed721cebe2c6d.png', 'code=oss', 100, 1, '2025-08-25 17:23:13.575', 1, '2025-08-25 17:23:13.575', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1959915474150088705, '000000', '2025/08/25/9732e505c5fd40c1bac51cdf3ec38108.png', '2025-08-25-17-47-14_34013.png', '.png', 'http://***************:9104/gzyw/2025/08/25/9732e505c5fd40c1bac51cdf3ec38108.png', 'code=oss', 100, 1, '2025-08-25 17:47:17.163', 1, '2025-08-25 17:47:17.163', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960154932615061505, '000000', '2025/08/26/c8f99f7579dd45e6b5dda682acd382a3.jpg', '34c8ffacebd745e3ab27f623147a8cc2.jpg', '.jpg', 'http://***************:9104/gzyw/2025/08/26/c8f99f7579dd45e6b5dda682acd382a3.jpg', 'code=dsrZp,dsrId=31', 100, 1, '2025-08-26 09:38:48.51', 1, '2025-08-26 09:38:48.51', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960225952567074817, '000000', '2025/08/26/2119ba7e9c4944c79d10d647d5c7a401.doc', 'maker1756189250990_vqrIS9ga.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/2119ba7e9c4944c79d10d647d5c7a401.doc', '', 100, 1, '2025-08-26 14:21:00.986', 1, '2025-08-26 14:21:00.986', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960226070565429250, '000000', '2025/08/26/ce2da9388c5b4add8e3fc7360ae17353.doc', 'maker1756189283830_DCpnLfn6.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/ce2da9388c5b4add8e3fc7360ae17353.doc', '', 100, 1, '2025-08-26 14:21:29.119', 1, '2025-08-26 14:21:29.119', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960226106460282881, '000000', '2025/08/26/5fe2c6f2ddc04eb2ab3f2e6a3487f151.doc', 'maker1756189294587_N4Nhva0E.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/5fe2c6f2ddc04eb2ab3f2e6a3487f151.doc', '', 100, 1, '2025-08-26 14:21:37.677', 1, '2025-08-26 14:21:37.677', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960227599179522050, '000000', '2025/08/26/464352deafd04212abe2201f2e3cefd3.doc', 'maker1756189645149_0Hh1Uqon.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/464352deafd04212abe2201f2e3cefd3.doc', '', 100, 1, '2025-08-26 14:27:33.578', 1, '2025-08-26 14:27:33.578', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960359265189826561, '000000', '2025/08/26/69c363fe1a6046b1b74acc892a84f602.doc', '现场记录.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/69c363fe1a6046b1b74acc892a84f602.doc', 'code=oss', 100, 1, '2025-08-26 23:10:45.206', 1, '2025-08-26 23:10:45.206', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960362903413010434, '000000', '2025/08/26/87655ea09f594faf802a15929424fb4e.doc', '现场记录.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/87655ea09f594faf802a15929424fb4e.doc', 'code=oss', 100, 1, '2025-08-26 23:25:12.615', 1, '2025-08-26 23:25:12.615', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960364668493574146, '000000', '2025/08/26/30b19de300774e35a9ffa49a9733dbe0.doc', 'maker1756222308369_UTVrJKZB.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/30b19de300774e35a9ffa49a9733dbe0.doc', '', 100, 1, '2025-08-26 23:32:13.448', 1, '2025-08-26 23:32:13.448', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960364761946861569, '000000', '2025/08/26/a5701d7e5275469fbcc879d3d445ab95.doc', 'maker1756222340286_zrRtrvya.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/a5701d7e5275469fbcc879d3d445ab95.doc', '', 100, 1, '2025-08-26 23:32:35.721', 1, '2025-08-26 23:32:35.721', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960365970648739841, '000000', '2025/08/26/e518e58f9ce2400da8e82ba62ed60f6c.doc', 'maker1756222622385_edC5ud33.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/e518e58f9ce2400da8e82ba62ed60f6c.doc', '', 100, 1, '2025-08-26 23:37:23.901', 1, '2025-08-26 23:37:23.901', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960367544066486273, '000000', '2025/08/26/8546addc9a494ba8bc7012fe4d685739.doc', 'maker1756222990793_M6PE9zNq.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/8546addc9a494ba8bc7012fe4d685739.doc', '', 100, 1, '2025-08-26 23:43:39.036', 1, '2025-08-26 23:43:39.036', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960367987219869698, '000000', '2025/08/26/9a951848f0004b079249e66893ec9ec0.doc', '现场记录.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/9a951848f0004b079249e66893ec9ec0.doc', 'code=oss', 100, 1, '2025-08-26 23:45:24.69', 1, '2025-08-26 23:45:24.69', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960368264035545089, '000000', '2025/08/26/ceece1a3adc1430b87567bf68a3f01f4.doc', 'maker1756223175130_fDpJgXva.doc', '.doc', 'http://***************:9104/gzyw/2025/08/26/ceece1a3adc1430b87567bf68a3f01f4.doc', '', NULL, 1, '2025-08-26 23:46:30.69', NULL, '2025-08-26 23:46:30.69', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960641368393101314, '000000', '2025/08/27/cb7cc0380be046399c04859da3778fa8.doc', 'maker1756288279246_26oAVeDK.doc', '.doc', 'http://***************:9104/gzyw/2025/08/27/cb7cc0380be046399c04859da3778fa8.doc', '', 100, 1, '2025-08-27 17:51:43.837', 1, '2025-08-27 17:51:43.837', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960647252280193025, '000000', '2025/08/27/a47c18d5c8204511890b0aacfa90585a.doc', 'maker1756289691952_6V7DBrPw.doc', '.doc', 'http://***************:9104/gzyw/2025/08/27/a47c18d5c8204511890b0aacfa90585a.doc', '', 100, 1, '2025-08-27 18:15:06.681', 1, '2025-08-27 18:15:06.681', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960647880549183489, '000000', '2025/08/27/217f19fb20ec4322a589dba7f1cab723.doc', 'maker1756289846898_BHQ2QKzo.doc', '.doc', 'http://***************:9104/gzyw/2025/08/27/217f19fb20ec4322a589dba7f1cab723.doc', '', 100, 1, '2025-08-27 18:17:36.456', 1, '2025-08-27 18:17:36.456', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960648117841932289, '000000', '2025/08/27/77ce25b64280488387642f954ca2e277.doc', 'maker1756289905590_iaXyNqkg.doc', '.doc', 'http://***************:9104/gzyw/2025/08/27/77ce25b64280488387642f954ca2e277.doc', '', 100, 1, '2025-08-27 18:18:33.037', 1, '2025-08-27 18:18:33.037', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960668039909412865, '000000', '2025/08/27/76143a706c19427fac74519fb7d435f6.png', 'wechat_2025-08-06_001020_071.png', '.png', 'http://***************:9104/gzyw/2025/08/27/76143a706c19427fac74519fb7d435f6.png', 'code=oss', 100, 1, '2025-08-27 19:37:42.822', 1, '2025-08-27 19:37:42.822', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960668186907185153, '000000', '2025/08/27/a7a4992ac1784bda9ad1029abe87e1f5.png', 'wechat_2025-08-06_001020_071.png', '.png', 'http://***************:9104/gzyw/2025/08/27/a7a4992ac1784bda9ad1029abe87e1f5.png', 'code=oss', 100, 1, '2025-08-27 19:38:17.877', 1, '2025-08-27 19:38:17.877', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960889554198118401, '000000', '2025/08/28/500a3e3123bc44b4b35a8e2ff823ba92.doc', 'maker1756347457704_fzfWLYiw.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/500a3e3123bc44b4b35a8e2ff823ba92.doc', '', 100, 1, '2025-08-28 10:17:55.947', 1, '2025-08-28 10:17:55.947', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960893871990820866, '000000', '2025/08/28/3a77ebe0d20146c4815962690fa1a678.doc', 'maker1756348491682_lWcBzVkY.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/3a77ebe0d20146c4815962690fa1a678.doc', '', 100, 1, '2025-08-28 10:35:05.388', 1, '2025-08-28 10:35:05.388', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960893965645434882, '000000', '2025/08/28/442d87d236634accba3d88907f7763a3.doc', 'maker1756348518438_fbOboxE2.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/442d87d236634accba3d88907f7763a3.doc', '', 100, 1, '2025-08-28 10:35:27.719', 1, '2025-08-28 10:35:27.719', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960895310163136514, '000000', '2025/08/28/0780e886aec74a0db7bf0660d4f22ba7.doc', 'maker1756348836504_pMvDXl5C.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/0780e886aec74a0db7bf0660d4f22ba7.doc', '', 100, 1, '2025-08-28 10:40:48.283', 1, '2025-08-28 10:40:48.283', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960896186214830081, '000000', '2025/08/28/9c598cb3b5cc445584a8580944e7fe2c.doc', 'maker1756349047803_vWWuOTM4.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/9c598cb3b5cc445584a8580944e7fe2c.doc', '', 100, 1, '2025-08-28 10:44:17.142', 1, '2025-08-28 10:44:17.142', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960987469134483457, '000000', '2025/08/28/60f945097f6d459480a08ed263d37516.doc', 'maker1756370809323_nxRTwFjQ.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/60f945097f6d459480a08ed263d37516.doc', '', 100, 1, '2025-08-28 16:47:00.684', 1, '2025-08-28 16:47:00.684', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960988495350648833, '000000', '2025/08/28/73a6f35a9540401f8a358bddde582fe6.doc', 'maker1756371052772_uBnzowxS.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/73a6f35a9540401f8a358bddde582fe6.doc', '', 100, 1, '2025-08-28 16:51:05.357', 1, '2025-08-28 16:51:05.357', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960989485437079553, '000000', '2025/08/28/47592891a3d042d58dd9e6b51d820be0.doc', 'maker1756371289981_ZE45RlFH.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/47592891a3d042d58dd9e6b51d820be0.doc', '', 100, 1, '2025-08-28 16:55:01.417', 1, '2025-08-28 16:55:01.417', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960991051237879809, '000000', '2025/08/28/495888c3a18e45e2b1b8db564f66b8d1.doc', 'maker1756371650264_0CBBuWSg.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/495888c3a18e45e2b1b8db564f66b8d1.doc', '', 100, 1, '2025-08-28 17:01:14.724', 1, '2025-08-28 17:01:14.724', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960991574913511426, '000000', '2025/08/28/e23dab8c8eca4fdda3c3d81278e5d8b8.doc', 'maker1756371788259_a7TOpBc6.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/e23dab8c8eca4fdda3c3d81278e5d8b8.doc', '', 100, 1, '2025-08-28 17:03:19.588', 1, '2025-08-28 17:03:19.588', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960992709271416834, '000000', '2025/08/28/8daa4b88dc104d54a2d1dd50b61795a9.doc', 'maker1756372048800_NaB2Dlm3.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/8daa4b88dc104d54a2d1dd50b61795a9.doc', '', 100, 1, '2025-08-28 17:07:50.034', 1, '2025-08-28 17:07:50.034', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1960998513194102785, '000000', '2025/08/28/439d283752cb46b79b254abd9c09edb8.doc', 'maker1756373442984_nzMZcAnU.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/439d283752cb46b79b254abd9c09edb8.doc', '', 100, 1, '2025-08-28 17:30:53.793', 1, '2025-08-28 17:30:53.793', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961056720990158849, '000000', '2025/08/28/b6f30d6dcefb40e0828cda3ebd7b47d2.doc', 'maker1756387324081_8tKtHpeJ.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/b6f30d6dcefb40e0828cda3ebd7b47d2.doc', '', NULL, 1, '2025-08-28 21:22:11.62', NULL, '2025-08-28 21:22:11.62', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961056987978579969, '000000', '2025/08/28/1bac7234908c40148f4fe68c300818ed.doc', 'maker1756387390827_ifzgqRnc.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/1bac7234908c40148f4fe68c300818ed.doc', '', 100, 1, '2025-08-28 21:23:15.283', 1, '2025-08-28 21:23:15.283', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961057463356801026, '000000', '2025/08/28/dae9da36a4834051a1ed20851eab211e.doc', 'maker1756387504046_wmMOQ7Rh.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/dae9da36a4834051a1ed20851eab211e.doc', '', 100, 1, '2025-08-28 21:25:08.607', 1, '2025-08-28 21:25:08.607', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961060418073157634, '000000', '2025/08/28/c2d1a5f9499b49f18509e048b7c3aa06.doc', 'maker1756388198640_yqev6Vsm.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/c2d1a5f9499b49f18509e048b7c3aa06.doc', '', 100, 1, '2025-08-28 21:36:53.067', 1, '2025-08-28 21:36:53.067', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961064215615815681, '000000', '2025/08/28/9e08ce354cce4a67be6a8e5ebf24dfd7.doc', 'maker1756389110742_zsDEuqlf.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/9e08ce354cce4a67be6a8e5ebf24dfd7.doc', '', 100, 1, '2025-08-28 21:51:58.479', 1, '2025-08-28 21:51:58.479', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961064252815097857, '000000', '2025/08/28/5d37c86f036a451994e420ffa7977dad.doc', 'maker1756389121724_maGhkad7.doc', '.doc', 'http://***************:9104/gzyw/2025/08/28/5d37c86f036a451994e420ffa7977dad.doc', '', 100, 1, '2025-08-28 21:52:07.34', 1, '2025-08-28 21:52:07.34', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961463670862364674, '000000', '2025/08/30/fa28eb78c7134e9cb1691e3413f3ccbc.jpeg', '啊.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/30/fa28eb78c7134e9cb1691e3413f3ccbc.jpeg', 'code=oss', 100, 1, '2025-08-30 00:19:16.023', 1, '2025-08-30 00:19:16.023', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961463863976509442, '000000', '2025/08/30/377c01c41755426089f27dbcdc1a7c99.jpeg', '4f3f922780b5cb487f50bd2f7a923d53.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/08/30/377c01c41755426089f27dbcdc1a7c99.jpeg', 'code=oss', 100, 1, '2025-08-30 00:20:02.066', 1, '2025-08-30 00:20:02.066', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1961974832355090434, '000000', '2025/08/31/43d5252aadd84547bd51709c5229be91.doc', 'maker1756606203985_CpzmiH01.doc', '.doc', 'http://***************:9104/gzyw/2025/08/31/43d5252aadd84547bd51709c5229be91.doc', '', 100, 1, '2025-08-31 10:10:26.421', 1, '2025-08-31 10:10:26.421', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962332014464065537, '000000', '2025/09/01/76ddc4637d324b85b363679e06c51ec5.jpeg', '3d8a16d11944689f9f210d2675ead230.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/09/01/76ddc4637d324b85b363679e06c51ec5.jpeg', 'code=oss', 100, 1, '2025-09-01 09:49:45.271', 1, '2025-09-01 09:49:45.271', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962332055585021954, '000000', '2025/09/01/c214bc2a115e44e4ac1aba3466a95cc8.jpeg', '6ac54ccb31a09a5c1223677ba07c283f.jpeg', '.jpeg', 'http://***************:9104/gzyw/2025/09/01/c214bc2a115e44e4ac1aba3466a95cc8.jpeg', 'code=oss', 100, 1, '2025-09-01 09:49:55.075', 1, '2025-09-01 09:49:55.075', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962436547701661697, '000000', '2025/09/01/496e5f05e8384d73aebac6de173f323e.png', '2025-09-01-16-44-59_99886.png', '.png', 'http://***************:9104/gzyw/2025/09/01/496e5f05e8384d73aebac6de173f323e.png', 'code=oss', 100, 1, '2025-09-01 16:45:07.941', 1, '2025-09-01 16:45:07.941', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962438669503586305, '000000', '2025/09/01/4fbc896e8937421ebba3739daebd16ad.png', '2025-09-01-16-53-32_12912.png', '.png', 'http://***************:9104/gzyw/2025/09/01/4fbc896e8937421ebba3739daebd16ad.png', 'code=oss', 100, 1, '2025-09-01 16:53:33.814', 1, '2025-09-01 16:53:33.814', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962439030742212610, '000000', '2025/09/01/534ab78b51fa4cd3934ca19131995e1a.png', '2025-09-01-16-54-59_99227.png', '.png', 'http://***************:9104/gzyw/2025/09/01/534ab78b51fa4cd3934ca19131995e1a.png', 'code=oss', 100, 1, '2025-09-01 16:54:59.956', 1, '2025-09-01 16:54:59.956', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962440746225131522, '000000', '2025/09/01/32afc8a6f5ad42c58e8761348702309f.png', '2025-09-01-17-01-48_08263.png', '.png', 'http://***************:9104/gzyw/2025/09/01/32afc8a6f5ad42c58e8761348702309f.png', 'code=oss', 100, 1, '2025-09-01 17:01:48.945', 1, '2025-09-01 17:01:48.945', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962443812588511234, '000000', '2025/09/01/40ccf3660f48478a87754001bda36777.docx', '公证在线业务H5页面设计.docx', '.docx', 'http://***************:9104/gzyw/2025/09/01/40ccf3660f48478a87754001bda36777.docx', 'code=oss', 100, 1, '2025-09-01 17:14:00.021', 1, '2025-09-01 17:14:00.021', 'minio');

INSERT INTO "public"."sys_oss" ("oss_id", "tenant_id", "file_name", "original_name", "file_suffix", "url", "ext1", "create_dept", "create_by", "create_time", "update_by", "update_time", "service") VALUES (1962444088020066305, '000000', '2025/09/01/8bdb2238c5a44537a021f82d5e1b1709.docx', '公证在线业务H5页面设计.docx', '.docx', 'http://***************:9104/gzyw/2025/09/01/8bdb2238c5a44537a021f82d5e1b1709.docx', 'code=oss', 100, 1, '2025-09-01 17:15:05.691', 1, '2025-09-01 17:15:05.691', 'minio');

UPDATE "public"."sys_oss" SET "tenant_id" = '000000', "file_name" = '2025/08/12/5045d31b57394d6ebc636329cd1f608d.doc', "original_name" = 'maker1754990741517_tl8eV60f.doc', "file_suffix" = '.doc', "url" = 'http://***************:9104/gzyw/2025/08/12/5045d31b57394d6ebc636329cd1f608d.doc', "ext1" = '', "create_dept" = 103, "create_by" = 1, "create_time" = '2025-08-12 17:25:51.743', "update_by" = 1, "update_time" = '2025-08-12 17:25:51.743', "service" = 'minio' WHERE "oss_id" = 1955199040455036929;
