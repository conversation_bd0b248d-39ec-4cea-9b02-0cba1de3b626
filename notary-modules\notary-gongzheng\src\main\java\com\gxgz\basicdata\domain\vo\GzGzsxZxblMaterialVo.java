package com.gxgz.basicdata.domain.vo;

import com.gxgz.basicdata.domain.GzGzsxZxblMaterial;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gxgz.common.excel.annotation.ExcelDictFormat;
import com.gxgz.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 在线办理材料管理视图对象 gz_gzsx_zxbl_material
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GzGzsxZxblMaterial.class)
public class GzGzsxZxblMaterialVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 材料名称
     */
    @ExcelProperty(value = "材料名称")
    private String materialName;

    /**
     * 类别：1-个人，2-企业
     */
    @ExcelProperty(value = "类别：1-个人，2-企业")
    private Long category;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    private String description;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
