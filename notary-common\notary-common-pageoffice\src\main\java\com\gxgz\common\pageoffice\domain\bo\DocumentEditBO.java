package com.gxgz.common.pageoffice.domain.bo;

import com.gxgz.common.core.domain.dto.ParamsDTO;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * 文档编辑业务对象
 *
 * <AUTHOR>
 */
@Data
public class DocumentEditBO {

    /**
     * 文档ID
     */
    @NotBlank(message = "文档ID不能为空")
    private String documentId;

    /**
     * 文档名称
     */
    @NotBlank(message = "文档名称不能为空")
    private String documentName;

    /**
     * 文档路径
     */
    private String documentPath;

    /**
     * 文档网络地址（优先级高于documentPath）
     */
    private String documentUrl;

    /**
     * 文档类型
     */
    @NotBlank(message = "文档类型不能为空")
    private String documentType;

    /**
     * 编辑模式
     */
    @NotBlank(message = "编辑模式不能为空")
    private String editMode;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否启用修订模式
     */
    private Boolean enableRevision = false;

    /**
     * 是否启用印章功能
     */
    private Boolean enableSeal = false;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 宽度
     */
    private String width = "100%";

    /**
     * 高度
     */
    private String height = "600px";

    /**
     * 自定义参数（用于文档内参数嵌入）
     */
    private Map<String, ParamsDTO> customParams;

    /**
     * 文档描述
     */
    private String description;

    /**
     * 是否自动保存
     */
    private Boolean autoSave = false;

    /**
     * 自动保存间隔（秒）
     */
    private Integer autoSaveInterval = 300;

    /**
     * 是否启用协同编辑
     */
    private Boolean enableCollaboration = false;

    /**
     * 业务CODE 对应 生成策略的 StrategyType 值
     */
    private String ywType;
    /**
     * 附件类别 对应 ZmclxxTypeEnum
     */
    private String fjlb;
    /**
     * 保存 文档类型  可选 word 、excel 、pdf 、ppt
     * */
    private String saveDocumentType;
}
