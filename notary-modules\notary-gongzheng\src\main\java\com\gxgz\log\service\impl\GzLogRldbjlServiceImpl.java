package com.gxgz.log.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.core.utils.MapstructUtils;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gxgz.dsr.domain.GzDsrxxZrr;
import com.gxgz.dsr.mapper.GzDsrxxZrrMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.gxgz.log.domain.bo.GzLogRldbjlBo;
import com.gxgz.log.domain.vo.GzLogRldbjlVo;
import com.gxgz.log.domain.GzLogRldbjl;
import com.gxgz.log.mapper.GzLogRldbjlMapper;
import com.gxgz.log.service.IGzLogRldbjlService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 日志-人脸对比记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@RequiredArgsConstructor
@Service
public class GzLogRldbjlServiceImpl implements IGzLogRldbjlService {

    private final GzLogRldbjlMapper baseMapper;
    private final GzDsrxxZrrMapper dsrxxZrrMapper;

    /**
     * 查询日志-人脸对比记录
     *
     * @param id 主键
     * @return 日志-人脸对比记录
     */
    @Override
    public GzLogRldbjlVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询日志-人脸对比记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 日志-人脸对比记录分页列表
     */
    @Override
    public TableDataInfo<GzLogRldbjlVo> queryPageList(GzLogRldbjlBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GzLogRldbjl> lqw = buildQueryWrapper(bo);
        Page<GzLogRldbjlVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            result.getRecords().forEach(item -> {
                GzDsrxxZrr dsrxxZrr = dsrxxZrrMapper.selectById(item.getDsrId());
                item.setDsrXm(dsrxxZrr != null ? dsrxxZrr.getXm() : null);
                item.setDsrXm(dsrxxZrr != null ? dsrxxZrr.getZjhm() : null);
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的日志-人脸对比记录列表
     *
     * @param bo 查询条件
     * @return 日志-人脸对比记录列表
     */
    @Override
    public List<GzLogRldbjlVo> queryList(GzLogRldbjlBo bo) {
        LambdaQueryWrapper<GzLogRldbjl> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GzLogRldbjl> buildQueryWrapper(GzLogRldbjlBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GzLogRldbjl> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GzLogRldbjl::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDbrl()), GzLogRldbjl::getDbrl, bo.getDbrl());
        lqw.eq(StringUtils.isNotBlank(bo.getDbjg()), GzLogRldbjl::getDbjg, bo.getDbjg());
        if (StringUtils.isNotBlank(bo.getDsrZjhm()) || StringUtils.isNotBlank(bo.getDsrXm())) {
            // 构建基础子查询
            StringBuilder subSql = new StringBuilder("dsr_id in (select gdr.id from gz_dsrxx_zrr gdr where ");
            List<Object> params2 = new ArrayList<>();
            boolean hasCondition = false;
            // 添加证件号码条件
            if (StringUtils.isNotBlank(bo.getDsrZjhm())) {
                subSql.append("gdr.zjhm like concat('%', {0}, '%')");
                params2.add(bo.getDsrZjhm());
                hasCondition = true;
            }
            // 添加姓名条件
            if (StringUtils.isNotBlank(bo.getDsrXm())) {
                // 如果已有条件，添加OR连接符
                if (hasCondition) {
                    subSql.append(" or ");
                }
                // 使用当前参数列表大小作为占位符索引
                subSql.append("gdr.xm like concat('%', {").append(params2.size()).append("}, '%')");
                params2.add(bo.getDsrXm());
            }
            subSql.append(")");
            // 应用条件
            lqw.apply(subSql.toString(), params2.toArray());
        }
        return lqw;
    }

    /**
     * 新增日志-人脸对比记录
     *
     * @param bo 日志-人脸对比记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GzLogRldbjlBo bo) {
        GzLogRldbjl add = MapstructUtils.convert(bo, GzLogRldbjl.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改日志-人脸对比记录
     *
     * @param bo 日志-人脸对比记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GzLogRldbjlBo bo) {
        GzLogRldbjl update = MapstructUtils.convert(bo, GzLogRldbjl.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GzLogRldbjl entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除日志-人脸对比记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
