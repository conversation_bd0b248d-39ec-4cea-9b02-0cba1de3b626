package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxZxblMaterialVo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblMaterialBo;
import com.gxgz.basicdata.service.IGzGzsxZxblMaterialService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 在线办理材料管理
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxZxblMaterial")
public class GzGzsxZxblMaterialController extends BaseController {

    private final IGzGzsxZxblMaterialService gzGzsxZxblMaterialService;

    /**
     * 查询在线办理材料管理列表
     */
    @SaCheckPermission("basicdata:gzsxZxblMaterial:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxZxblMaterialVo> list(GzGzsxZxblMaterialBo bo, PageQuery pageQuery) {
        return gzGzsxZxblMaterialService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出在线办理材料管理列表
     */
    @SaCheckPermission("basicdata:gzsxZxblMaterial:export")
    @Log(title = "在线办理材料管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxZxblMaterialBo bo, HttpServletResponse response) {
        List<GzGzsxZxblMaterialVo> list = gzGzsxZxblMaterialService.queryList(bo);
        ExcelUtil.exportExcel(list, "在线办理材料管理", GzGzsxZxblMaterialVo.class, response);
    }

    /**
     * 获取在线办理材料管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("basicdata:gzsxZxblMaterial:query")
    @GetMapping("/{id}")
    public R<GzGzsxZxblMaterialVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxZxblMaterialService.queryById(id));
    }

    /**
     * 新增在线办理材料管理
     */
    @SaCheckPermission("basicdata:gzsxZxblMaterial:add")
    @Log(title = "在线办理材料管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxZxblMaterialBo bo) {
        return toAjax(gzGzsxZxblMaterialService.insertByBo(bo));
    }

    /**
     * 修改在线办理材料管理
     */
    @SaCheckPermission("basicdata:gzsxZxblMaterial:edit")
    @Log(title = "在线办理材料管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxZxblMaterialBo bo) {
        return toAjax(gzGzsxZxblMaterialService.updateByBo(bo));
    }

    /**
     * 删除在线办理材料管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("basicdata:gzsxZxblMaterial:remove")
    @Log(title = "在线办理材料管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxZxblMaterialService.deleteWithValidByIds(List.of(ids), true));
    }
}
