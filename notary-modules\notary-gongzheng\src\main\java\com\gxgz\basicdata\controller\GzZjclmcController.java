package com.gxgz.basicdata.controller;

import java.util.List;

import com.gxgz.basicdata.domain.vo.GzGzsVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzZjclmcVo;
import com.gxgz.basicdata.domain.bo.GzZjclmcBo;
import com.gxgz.basicdata.service.IGzZjclmcService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 证据名称
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/zjclmc")
public class GzZjclmcController extends BaseController {

    private final IGzZjclmcService gzZjclmcService;

    /**
     * 查询证据名称列表
     */
//    @SaCheckPermission("basicdata:zjclmc:list")
    @GetMapping("/list")
    public TableDataInfo<GzZjclmcVo> list(GzZjclmcBo bo, PageQuery pageQuery) {
        return gzZjclmcService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出证据名称列表
     */
//    @SaCheckPermission("basicdata:zjclmc:export")
    @Log(title = "证据名称", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzZjclmcBo bo, HttpServletResponse response) {
        List<GzZjclmcVo> list = gzZjclmcService.queryList(bo);
        ExcelUtil.exportExcel(list, "证据名称", GzZjclmcVo.class, response);
    }

    /**
     * 获取证据名称详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:zjclmc:query")
    @GetMapping("/{id}")
    public R<GzZjclmcVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(gzZjclmcService.queryById(id));
    }

    /**
     * 新增证据名称
     */
//    @SaCheckPermission("basicdata:zjclmc:add")
    @Log(title = "证据名称", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzZjclmcBo bo) {
        return toAjax(gzZjclmcService.insertByBo(bo));
    }

    /**
     * 修改证据名称
     */
//    @SaCheckPermission("basicdata:zjclmc:edit")
    @Log(title = "证据名称", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzZjclmcBo bo) {
        return toAjax(gzZjclmcService.updateByBo(bo));
    }

    /**
     * 删除证据名称
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:zjclmc:remove")
    @Log(title = "证据名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzZjclmcService.deleteWithValidByIds(List.of(ids), true));
    }


//    @SaCheckPermission("basicdata:zjclmc:list")
    @GetMapping("/listTree")
    public R listTree(GzZjclmcBo bo) {
        List<GzZjclmcVo> zjclmcVoList = gzZjclmcService.listTree(bo);
        return R.ok("ok", zjclmcVoList);
    }

//    @SaCheckPermission("basicdata:zjclmc:list")
    @GetMapping("/list/exclude/{gzsId}")
    public R listExclude(@PathVariable(value = "id", required = false) Long id) {
        List<GzZjclmcVo> zjclmcVoList = gzZjclmcService.listExclude(id);
        return R.ok("ok", zjclmcVoList);
    }

}
