package com.gxgz.dsr.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.dsr.domain.vo.GzDsrxxZrrVo;
import com.gxgz.dsr.domain.bo.GzDsrxxZrrBo;
import com.gxgz.dsr.service.IGzDsrxxZrrService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 当事人-基本信息-自然人信息
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dsr/dsrxxZrr")
public class GzDsrxxZrrController extends BaseController {

    private final IGzDsrxxZrrService gzDsrxxZrrService;

    /**
     * 查询当事人-基本信息-自然人信息列表
     */
//    @SaCheckPermission("dsr:dsrxxZrr:list")
    @GetMapping("/list")
    public TableDataInfo<GzDsrxxZrrVo> list(GzDsrxxZrrBo bo, PageQuery pageQuery) {
        return gzDsrxxZrrService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出当事人-基本信息-自然人信息列表
     */
//    @SaCheckPermission("dsr:dsrxxZrr:export")
    @Log(title = "导出个人客户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzDsrxxZrrBo bo, HttpServletResponse response) {
        List<GzDsrxxZrrVo> list = gzDsrxxZrrService.queryList(bo);
        ExcelUtil.exportExcel(list, "个人客户信息", GzDsrxxZrrVo.class, response);
    }

    /**
     * 获取当事人-基本信息-自然人信息详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("dsr:dsrxxZrr:query")
    @GetMapping("/{id}")
    public R<GzDsrxxZrrVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzDsrxxZrrService.queryById(id));
    }

    /**
     * 通过身份证号获取当事人信息
     * <p>国内：身份证；
     * 国外：护照；</p>
     * @param IdcardNumber
     * @return
     */
    @GetMapping("/getInfoByIdcard")
    public R<GzDsrxxZrrVo> getInfoByIdcard(@NotNull(message = "IdcardNumber不能为空") String IdcardNumber) {
        return R.ok(gzDsrxxZrrService.queryByIdcardNumber(IdcardNumber));
    }

    /**
     * 新增当事人-基本信息-自然人信息
     */
//    @SaCheckPermission("dsr:dsrxxZrr:add")
    @Log(title = "当事人-基本信息-自然人信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzDsrxxZrrBo bo) {
        return toAjax(gzDsrxxZrrService.insertByBo(bo));
    }

    /**
     * 修改当事人-基本信息-自然人信息
     */
//    @SaCheckPermission("dsr:dsrxxZrr:edit")
    @Log(title = "当事人-基本信息-自然人信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzDsrxxZrrBo bo) {
        return toAjax(gzDsrxxZrrService.updateByBo(bo));
    }

    /**
     * 删除当事人-基本信息-自然人信息
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("dsr:dsrxxZrr:remove")
    @Log(title = "当事人-基本信息-自然人信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzDsrxxZrrService.deleteWithValidByIds(List.of(ids), true));
    }
}
