package com.gxgz.guichain.domain.bo;

import com.gxgz.common.core.enums.OnChainClassifyEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class GxBassChainBo {

    private String bizId;

    /**
     * 存证描述
     */
    @NotBlank(message = "存证描述不能为空")
    private String chainDec;

    /**
     * 存证数据
     */
    @NotBlank(message = "存证数据不能为空")
    private String attrJSONStr;

    /**
     * 上链分类
     */
    private OnChainClassifyEnum onChainClassifyEnum;
}
