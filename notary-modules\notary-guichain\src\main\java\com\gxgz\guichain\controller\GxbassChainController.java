package com.gxgz.guichain.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.guichain.componet.GxBaasComponent;
import com.gxgz.guichain.domain.GxbassChain;
import com.gxgz.guichain.domain.bo.EncryptBo;
import com.gxgz.guichain.domain.bo.GxBassChainBo;
//import com.gxgz.guichain.domain.bo.GxbassChainBo;
import com.gxgz.guichain.domain.bo.UploadChainFileBo;
import com.gxgz.guichain.domain.vo.GxbassChainVo;
import com.gxgz.guichain.service.IGxbassChainService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 桂链存证信息
 *
 */
@Validated
@RequiredArgsConstructor
@RestController
    @RequestMapping("/gxbass/chain")
public class GxbassChainController extends BaseController {

    private final IGxbassChainService iGxbassChainService;
    private final GxBaasComponent gxBaasComponent;

//    /**
//     * 查询桂链存证信息列表
//     */
//    @SaCheckPermission("gxbass:chain:list")
//    @GetMapping("/list")
//    public TableDataInfo<GxbassChainVo> list(GxbassChainBo bo, PageQuery pageQuery) {
//        return iGxbassChainService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出桂链存证信息列表
//     */
//    @SaCheckPermission("gxbass:chain:export")
//    @Log(title = "桂链存证信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(GxbassChainBo bo, HttpServletResponse response) {
//        List<GxbassChainVo> list = iGxbassChainService.queryList(bo);
//        ExcelUtil.exportExcel(list, "桂链存证信息", GxbassChainVo.class, response);
//    }
//
//    /**
//     * 获取桂链存证信息详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("gxbass:chain:query")
//    @GetMapping("/{id}")
//    public R<GxbassChainVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long id) {
//        return R.ok(iGxbassChainService.queryById(id));
//    }
//
//    /**
//     * 新增桂链存证信息
//     */
//    @SaCheckPermission("gxbass:chain:add")
//    @Log(title = "桂链存证信息", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody GxbassChainBo bo) {
//        return toAjax(iGxbassChainService.insertByBo(bo));
//    }
//
//
//
//    /**
//     * 删除桂链存证信息
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("gxbass:chain:remove")
//    @Log(title = "桂链存证信息", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ids) {
//        return toAjax(iGxbassChainService.deleteWithValidByIds(Arrays.asList(ids), true));
//    }

    /**
     * 上传文件桂链存证
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadFile")
    public R uploadFile(@RequestBody UploadChainFileBo bo) throws IOException {
        iGxbassChainService.uploadFileChain(bo);
        return R.ok("上链成功");
    }

    /**
     * 数据上链
     * @param bo
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadData")
    public R uploadData(@RequestBody GxBassChainBo bo) throws IOException {
        iGxbassChainService.uploadData(bo);
        return R.ok("上链成功");
    }
    /**
     * 获取存证详情
     * @param txHash
     * @return
     */
    @GetMapping("/getChinInfo/{txHash}")
    public R getChinInfo(@PathVariable("txHash") String txHash) {
        return R.ok(iGxbassChainService.getChinInfo(txHash));
    }
    /**
     * 存证证书预览
     * @return
     * @throws IOException
     */
    @GetMapping("/readCertificate")
    public R uploadFile(String txHash){
        String certificateUrl = gxBaasComponent.getCertificate(txHash);
        Map<String,String> map = new HashMap<>();
        map.put("certificateUrl",certificateUrl);
        return R.ok("操作成功",map);
    }

    /**
     * 存证证书下载
     * @param txHash
     * @param response
     * @throws IOException
     */
    @PostMapping("/downloadCertificate")
    public void downloadCertificate(String txHash , HttpServletResponse response) throws IOException {
        iGxbassChainService.downloadCertificate(txHash,response);
    }

    /**
     * 解密文件下载
     * @param txHash
     * @param response
     * @throws IOException
     */
    @PostMapping("/downloadDecryptFile")
    public void downloadDecryptFile(String txHash , HttpServletResponse response) throws IOException {
        iGxbassChainService.downloadDecryptFile(txHash,response);
    }

    @GetMapping("/getFileSuffix/{ossId}")
    public R getFileSuffix(@PathVariable("ossId") Long ossId) throws IOException {
        String fileSuffix = iGxbassChainService.getFileSuffix(ossId);
        Map<String,String> map = new HashMap<>();
        map.put("fileSuffix",fileSuffix);
        return R.ok("操作成功",map);
    }

    /**
     * 数据存证解密
     *
     */
    @PostMapping("/getDecryptData")
    public R getDecryptData(@RequestBody EncryptBo bo) throws IOException {
        return R.ok("操作成功",gxBaasComponent.getDecryptData(bo));
    }

}
