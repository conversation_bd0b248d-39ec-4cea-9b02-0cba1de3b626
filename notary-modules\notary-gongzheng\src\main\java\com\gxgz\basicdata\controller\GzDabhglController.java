package com.gxgz.basicdata.controller;

import java.util.List;

import com.gxgz.basicdata.domain.GzGzsxJspz;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.basicdata.domain.vo.GzDabhglVo;
import com.gxgz.basicdata.domain.bo.GzDabhglBo;
import com.gxgz.basicdata.service.IGzDabhglService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 档案号头
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/dabhgl")
public class GzDabhglController extends BaseController {

    private final IGzDabhglService gzDabhglService;

    /**
     * 查询档案号头列表
     */
//    @SaCheckPermission("basicdata:dabhgl:list")
    @GetMapping("/list")
    public TableDataInfo<GzDabhglVo> list(GzDabhglBo bo, PageQuery pageQuery) {
        return gzDabhglService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出档案号头列表
     */
//    @SaCheckPermission("basicdata:dabhgl:export")
    @Log(title = "档案号头", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzDabhglBo bo, HttpServletResponse response) {
        List<GzDabhglVo> list = gzDabhglService.queryList(bo);
        ExcelUtil.exportExcel(list, "档案号头", GzDabhglVo.class, response);
    }

    /**
     * 获取档案号头详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:dabhgl:query")
    @GetMapping("/{id}")
    public R<GzDabhglVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzDabhglService.queryById(id));
    }

    /**
     * 新增档案号头
     */
//    @SaCheckPermission("basicdata:dabhgl:add")
    @Log(title = "档案号头", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzDabhglBo bo) {
        return toAjax(gzDabhglService.insertByBo(bo));
    }

    /**
     * 修改档案号头
     */
//    @SaCheckPermission("basicdata:dabhgl:edit")
    @Log(title = "档案号头", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzDabhglBo bo) {
        return toAjax(gzDabhglService.updateByBo(bo));
    }

    /**
     * 删除档案号头
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:dabhgl:remove")
    @Log(title = "档案号头", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzDabhglService.deleteWithValidByIds(List.of(ids), true));
    }



}
