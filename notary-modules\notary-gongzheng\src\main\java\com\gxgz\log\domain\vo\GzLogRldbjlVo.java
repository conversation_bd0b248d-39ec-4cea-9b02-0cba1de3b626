package com.gxgz.log.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gxgz.log.domain.GzLogRldbjl;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gxgz.common.excel.annotation.ExcelDictFormat;
import com.gxgz.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 日志-人脸对比记录视图对象 gz_log_rldbjl
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GzLogRldbjl.class)
public class GzLogRldbjlVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long id;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 对比人脸图片
     */
    @ExcelProperty(value = "对比人脸图片")
    private String dbrl;

    /**
     * 对比结果
     */
    @ExcelProperty(value = "对比结果")
    private String dbjg;
    /**
     * 当事人ID
     */
    private Long dsrId;
    /**
     * 当事人姓名
     */
    private String dsrXm;
    /**
     * 对比指数
     */
    private String dbzs;
    /**
     * 对比日期
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dbrq;

    /**
     * 当事人身份证号
     */
    private String dsrZjhm;

}
