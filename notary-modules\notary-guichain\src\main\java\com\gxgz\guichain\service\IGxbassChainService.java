package com.gxgz.guichain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.guichain.domain.GxbassChain;
import com.gxgz.guichain.domain.bo.GxBassChainBo;
//import com.gxgz.guichain.domain.bo.GxbassChainBo;
import com.gxgz.guichain.domain.bo.UploadChainFileBo;
import com.gxgz.guichain.domain.vo.GxDetailVo;
import com.gxgz.guichain.domain.vo.GxbassChainVo;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 桂链存证信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface IGxbassChainService  {

//    /**
//     * 查询桂链存证信息
//     */
//    GxbassChainVo queryById(Long id);
//
//    /**
//     * 查询桂链存证信息列表
//     */
//    TableDataInfo<GxbassChainVo> queryPageList(GxbassChainBo bo, PageQuery pageQuery);
//
//    /**
//     * 查询桂链存证信息列表
//     */
//    List<GxbassChainVo> queryList(GxbassChainBo bo);
//
//    /**
//     * 新增桂链存证信息
//     */
//    Boolean insertByBo(GxbassChainBo bo);
//
//    /**
//     * 修改桂链存证信息
//     */
//    Boolean updateByBo(GxbassChainBo bo);
//
//    /**
//     * 校验并批量删除桂链存证信息信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void uploadFileChain( UploadChainFileBo bo) throws IOException;

    void downloadCertificate(String txHash, HttpServletResponse response) throws UnsupportedEncodingException;

    void uploadData(GxBassChainBo bo);

    void downloadDecryptFile(String txHash, HttpServletResponse response) throws UnsupportedEncodingException;

    String getFileSuffix(Long ossId);

    GxDetailVo getChinInfo(String txHash);
}
