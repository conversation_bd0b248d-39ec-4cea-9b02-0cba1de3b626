package com.gxgz.guichain.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "gxbaas")
public class GxBaasConfig {
    /**
     * 配置Key
     */
    private String apiKey;
    /**
     * 配置密钥
     */
    private String apiSecret;
    /**
     * 登录url，获取token
     */
    private String loginUrl;
    /**
     * 数据存证url
     */
    private String uploadDataUrl;
    /**
     * 获取数据存证列表url
     */
    private String getProofDataUrl;
    /**
     * 数据存证解密url
     */
    private String decryptDataUrl;
    /**
     * 上传文件存证url
     */
    private String uploadFileUrl;
    /**
     * 获取文件存证列表url
     */
    private String getFileDataUrl;
    /**
     * 获取交易详情url
     */
    private String getDetailUrl;
    /**
     * 验证签名url
     */
    private String signCheckUrl;
    /**
     * 获取证书文件url
     */
    private String getCertificateUrl;
    /**
     * 下载证书文件url
     */
    private String downloadCertificateUrl;

    /**
     * 解密文件存证url
     */
    private String decryptFileUrl;
    /**
     * 通道ID
     */
    private String channelId;
}
