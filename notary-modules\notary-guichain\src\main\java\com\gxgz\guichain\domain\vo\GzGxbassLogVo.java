package com.gxgz.guichain.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gxgz.guichain.domain.GzGxbassLog;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gxgz.common.excel.annotation.ExcelDictFormat;
import com.gxgz.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 公证-桂链-上链日志视图对象 gz_gxbass_log
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GzGxbassLog.class)
public class GzGxbassLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long id;

    /**
     * 上链描述
     */
    @ExcelProperty(value = "上链描述")
    private String chainDec;

    /**
     * 上链数据
     */
    @ExcelProperty(value = "上链数据")
    private String chainData;

    /**
     * 上链文件
     */
    @ExcelProperty(value = "上链文件")
    private Long ossId;

    /**
     * 存证类型 1文件存证 2 数据存证
     */
    @ExcelProperty(value = "存证类型 1文件存证 2 数据存证")
    private Long chainType;

    /**
     * 虚拟通道ID，非虚拟通道加密方式返回空
     */
    @ExcelProperty(value = "虚拟通道ID，非虚拟通道加密方式返回空")
    private String channelId;

    /**
     * 加密方式
     */
    @ExcelProperty(value = "加密方式")
    private Long encryptMode;

    /**
     * 交易哈希
     */
    @ExcelProperty(value = "交易哈希")
    private String txHash;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 业务ID
     */
    @ExcelProperty(value = "业务ID")
    private String bizId;

    /**
     * 业务分类
     */
    @ExcelProperty(value = "业务分类")
    private String bizClassify;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
