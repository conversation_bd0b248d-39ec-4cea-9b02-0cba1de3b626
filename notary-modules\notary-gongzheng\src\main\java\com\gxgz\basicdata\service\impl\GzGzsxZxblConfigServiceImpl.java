package com.gxgz.basicdata.service.impl;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.core.utils.MapstructUtils;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblConfigBo;
import com.gxgz.basicdata.domain.vo.GzGzsxZxblConfigVo;
import com.gxgz.basicdata.domain.GzGzsxZxblConfig;
import com.gxgz.basicdata.mapper.GzGzsxZxblConfigMapper;
import com.gxgz.basicdata.service.IGzGzsxZxblConfigService;

import java.util.List;
import java.util.Collection;

/**
 * 在线办理配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@RequiredArgsConstructor
@Service
public class GzGzsxZxblConfigServiceImpl implements IGzGzsxZxblConfigService {

    private final GzGzsxZxblConfigMapper baseMapper;

    /**
     * 查询在线办理配置
     *
     * @param id 主键
     * @return 在线办理配置
     */
    @Override
    public GzGzsxZxblConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询在线办理配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 在线办理配置分页列表
     */
    @Override
    public TableDataInfo<GzGzsxZxblConfigVo> queryPageList(GzGzsxZxblConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GzGzsxZxblConfig> lqw = buildQueryWrapper(bo);
        Page<GzGzsxZxblConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的在线办理配置列表
     *
     * @param bo 查询条件
     * @return 在线办理配置列表
     */
    @Override
    public List<GzGzsxZxblConfigVo> queryList(GzGzsxZxblConfigBo bo) {
        LambdaQueryWrapper<GzGzsxZxblConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GzGzsxZxblConfig> buildQueryWrapper(GzGzsxZxblConfigBo bo) {
        LambdaQueryWrapper<GzGzsxZxblConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GzGzsxZxblConfig::getId);
        lqw.like(StringUtils.isNotBlank(bo.getGzsxName()), GzGzsxZxblConfig::getGzsxName, bo.getGzsxName());
        lqw.like(StringUtils.isNotBlank(bo.getGzsxCode()), GzGzsxZxblConfig::getGzsxCode, bo.getGzsxCode());
        lqw.eq(bo.getGzsxId() != null, GzGzsxZxblConfig::getGzsxId, bo.getGzsxId());
        lqw.eq(bo.getIsOnlineHandle() != null, GzGzsxZxblConfig::getIsOnlineHandle, bo.getIsOnlineHandle());
        lqw.eq(bo.getIsEnableHandle() != null, GzGzsxZxblConfig::getIsEnableHandle, bo.getIsEnableHandle());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialIds()), GzGzsxZxblConfig::getMaterialIds, bo.getMaterialIds());
        return lqw;
    }

    /**
     * 新增在线办理配置
     *
     * @param bo 在线办理配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GzGzsxZxblConfigBo bo) {
        GzGzsxZxblConfig add = MapstructUtils.convert(bo, GzGzsxZxblConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改在线办理配置
     *
     * @param bo 在线办理配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GzGzsxZxblConfigBo bo) {
        GzGzsxZxblConfig update = MapstructUtils.convert(bo, GzGzsxZxblConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GzGzsxZxblConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除在线办理配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据公证事项ID查询在线办理配置
     *
     * @param gzsxId 公证事项ID
     * @return 在线办理配置
     */
    @Override
    public GzGzsxZxblConfigVo queryByGzsxId(Long gzsxId) {
        LambdaQueryWrapper<GzGzsxZxblConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(GzGzsxZxblConfig::getGzsxId, gzsxId);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 批量保存在线办理配置
     *
     * @param boList 在线办理配置列表
     * @return 是否保存成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<GzGzsxZxblConfigBo> boList) {
        if (boList == null || boList.isEmpty()) {
            return false;
        }
        
        for (GzGzsxZxblConfigBo bo : boList) {
            // 根据公证事项ID查询是否已存在配置
            GzGzsxZxblConfigVo existConfig = queryByGzsxId(bo.getGzsxId());
            if (existConfig != null) {
                // 存在则更新
                bo.setId(existConfig.getId());
                if (!updateByBo(bo)) {
                    throw new RuntimeException("批量更新在线办理配置失败");
                }
            } else {
                // 不存在则新增
                if (!insertByBo(bo)) {
                    throw new RuntimeException("批量新增在线办理配置失败");
                }
            }
        }
        return true;
    }

    /**
     * 切换在线办理状态
     *
     * @param id              配置ID
     * @param isOnlineHandle  是否在线办理
     * @return 是否切换成功
     */
    @Override
    public Boolean toggleOnlineHandle(Long id, Long isOnlineHandle) {
        GzGzsxZxblConfig config = baseMapper.selectById(id);
        if (config == null) {
            return false;
        }
        config.setIsOnlineHandle(isOnlineHandle);
        return baseMapper.updateById(config) > 0;
    }

    /**
     * 切换启用办理状态
     *
     * @param id             配置ID
     * @param isEnableHandle 是否启用办理
     * @return 是否切换成功
     */
    @Override
    public Boolean toggleEnableHandle(Long id, Long isEnableHandle) {
        GzGzsxZxblConfig config = baseMapper.selectById(id);
        if (config == null) {
            return false;
        }
        config.setIsEnableHandle(isEnableHandle);
        return baseMapper.updateById(config) > 0;
    }
}
