package com.gxgz.basicdata.service.impl;
import com.gxgz.basicdata.domain.GzGzsxZxblConfig;
import com.gxgz.basicdata.mapper.GzGzsxZxblConfigMapper;
import com.gxgz.common.core.exception.ServiceException;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.core.utils.MapstructUtils;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblMaterialBo;
import com.gxgz.basicdata.domain.vo.GzGzsxZxblMaterialVo;
import com.gxgz.basicdata.domain.GzGzsxZxblMaterial;
import com.gxgz.basicdata.mapper.GzGzsxZxblMaterialMapper;
import com.gxgz.basicdata.service.IGzGzsxZxblMaterialService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 在线办理材料管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@RequiredArgsConstructor
@Service
public class GzGzsxZxblMaterialServiceImpl implements IGzGzsxZxblMaterialService {

    private final GzGzsxZxblMaterialMapper baseMapper;

    /**
     * 查询在线办理材料管理
     *
     * @param id 主键
     * @return 在线办理材料管理
     */
    @Override
    public GzGzsxZxblMaterialVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询在线办理材料管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 在线办理材料管理分页列表
     */
    @Override
    public TableDataInfo<GzGzsxZxblMaterialVo> queryPageList(GzGzsxZxblMaterialBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GzGzsxZxblMaterial> lqw = buildQueryWrapper(bo);
        Page<GzGzsxZxblMaterialVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的在线办理材料管理列表
     *
     * @param bo 查询条件
     * @return 在线办理材料管理列表
     */
    @Override
    public List<GzGzsxZxblMaterialVo> queryList(GzGzsxZxblMaterialBo bo) {
        LambdaQueryWrapper<GzGzsxZxblMaterial> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GzGzsxZxblMaterial> buildQueryWrapper(GzGzsxZxblMaterialBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GzGzsxZxblMaterial> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GzGzsxZxblMaterial::getId);
        lqw.like(StringUtils.isNotBlank(bo.getMaterialName()), GzGzsxZxblMaterial::getMaterialName, bo.getMaterialName());
        lqw.eq(bo.getCategory() != null, GzGzsxZxblMaterial::getCategory, bo.getCategory());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), GzGzsxZxblMaterial::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增在线办理材料管理
     *
     * @param bo 在线办理材料管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GzGzsxZxblMaterialBo bo) {
        GzGzsxZxblMaterial add = MapstructUtils.convert(bo, GzGzsxZxblMaterial.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改在线办理材料管理
     *
     * @param bo 在线办理材料管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GzGzsxZxblMaterialBo bo) {
        GzGzsxZxblMaterial update = MapstructUtils.convert(bo, GzGzsxZxblMaterial.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GzGzsxZxblMaterial entity){
        //TODO 做一些数据校验,如唯一约束
    }


    private   final GzGzsxZxblConfigMapper gzGzsxZxblConfigMapper;
    /**
     * 校验并批量删除在线办理材料管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
         }
        LambdaQueryWrapper<GzGzsxZxblConfig> queryWrapper = new LambdaQueryWrapper<>();
        boolean first = true;

        for (Long id : ids) {
            // 使用 PostgreSQL 的数组包含判断
            String sql = "ARRAY[" + id + "] <@ string_to_array(material_ids, ',')::bigint[]";
            if (first) {
                queryWrapper.apply(sql);
                first = false;
            } else {
                queryWrapper.or().apply(sql);
            }
        }
        long count = gzGzsxZxblConfigMapper.selectCount(queryWrapper);
        if(count > 0){
            throw new ServiceException("存在关联配置，不能删除");
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
