package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxSfbzVo;
import com.gxgz.basicdata.domain.bo.GzGzsxSfbzBo;
import com.gxgz.basicdata.service.IGzGzsxSfbzService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 收费标准
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxSfbz")
public class GzGzsxSfbzController extends BaseController {

    private final IGzGzsxSfbzService gzGzsxSfbzService;

    /**
     * 查询收费标准列表
     */
//    @SaCheckPermission("basicdata:gzsxSfbz:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxSfbzVo> list(GzGzsxSfbzBo bo, PageQuery pageQuery) {
        return gzGzsxSfbzService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出收费标准列表
     */
//    @SaCheckPermission("basicdata:gzsxSfbz:export")
    @Log(title = "收费标准", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxSfbzBo bo, HttpServletResponse response) {
        List<GzGzsxSfbzVo> list = gzGzsxSfbzService.queryList(bo);
        ExcelUtil.exportExcel(list, "收费标准", GzGzsxSfbzVo.class, response);
    }

    /**
     * 获取收费标准详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:gzsxSfbz:query")
    @GetMapping("/{id}")
    public R<GzGzsxSfbzVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxSfbzService.queryById(id));
    }

    /**
     * 新增收费标准
     */
//    @SaCheckPermission("basicdata:gzsxSfbz:add")
    @Log(title = "收费标准", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxSfbzBo bo) {
        return toAjax(gzGzsxSfbzService.insertByBo(bo));
    }

    /**
     * 修改收费标准
     */
//    @SaCheckPermission("basicdata:gzsxSfbz:edit")
    @Log(title = "收费标准", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxSfbzBo bo) {
        return toAjax(gzGzsxSfbzService.updateByBo(bo));
    }

    /**
     * 删除收费标准
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:gzsxSfbz:remove")
    @Log(title = "收费标准", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxSfbzService.deleteWithValidByIds(List.of(ids), true));
    }
}
