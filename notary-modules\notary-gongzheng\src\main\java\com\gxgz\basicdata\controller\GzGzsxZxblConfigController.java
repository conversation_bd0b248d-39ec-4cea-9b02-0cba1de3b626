package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxZxblConfigVo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblConfigBo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblConfigToggleBo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblMaterialBo;
import com.gxgz.basicdata.domain.vo.GzGzsxZxblMaterialVo;
import com.gxgz.basicdata.service.IGzGzsxZxblConfigService;
import com.gxgz.basicdata.service.IGzGzsxZxblMaterialService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 在线办理配置
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxZxblConfig")
public class GzGzsxZxblConfigController extends BaseController {

    private final IGzGzsxZxblConfigService gzGzsxZxblConfigService;
    private final IGzGzsxZxblMaterialService gzGzsxZxblMaterialService;

    /**
     * 查询在线办理配置列表
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxZxblConfigVo> list(GzGzsxZxblConfigBo bo, PageQuery pageQuery) {
        return gzGzsxZxblConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出在线办理配置列表
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:export")
    @Log(title = "在线办理配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxZxblConfigBo bo, HttpServletResponse response) {
        List<GzGzsxZxblConfigVo> list = gzGzsxZxblConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "在线办理配置", GzGzsxZxblConfigVo.class, response);
    }

    /**
     * 获取在线办理配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:query")
    @GetMapping("/{id}")
    public R<GzGzsxZxblConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxZxblConfigService.queryById(id));
    }

    /**
     * 新增在线办理配置
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:add")
    @Log(title = "在线办理配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxZxblConfigBo bo) {
        return toAjax(gzGzsxZxblConfigService.insertByBo(bo));
    }

    /**
     * 修改在线办理配置
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:edit")
    @Log(title = "在线办理配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxZxblConfigBo bo) {
        return toAjax(gzGzsxZxblConfigService.updateByBo(bo));
    }

    /**
     * 删除在线办理配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:remove")
    @Log(title = "在线办理配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxZxblConfigService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据公证事项ID查询在线办理配置
     *
     * @param gzsxId 公证事项ID
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:query")
    @GetMapping("/gzsx/{gzsxId}")
    public R<GzGzsxZxblConfigVo> getByGzsxId(@NotNull(message = "公证事项ID不能为空")
                                            @PathVariable Long gzsxId) {
        return R.ok(gzGzsxZxblConfigService.queryByGzsxId(gzsxId));
    }

    /**
     * 批量保存在线办理配置
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:add")
    @Log(title = "在线办理配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batch")
    public R<Void> batchSave(@Validated(AddGroup.class) @RequestBody List<GzGzsxZxblConfigBo> boList) {
        return toAjax(gzGzsxZxblConfigService.batchSave(boList));
    }

    /**
     * 切换在线办理状态
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:edit")
    @Log(title = "在线办理配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/toggle-online")
    public R<Void> toggleOnlineHandle(@Validated @RequestBody GzGzsxZxblConfigToggleBo bo) {
        return toAjax(gzGzsxZxblConfigService.toggleOnlineHandle(bo.getId(), bo.getIsOnlineHandle()));
    }

    /**
     * 切换启用办理状态
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:edit")
    @Log(title = "在线办理配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/toggle-enable")
    public R<Void> toggleEnableHandle(@Validated @RequestBody GzGzsxZxblConfigToggleBo bo) {
        return toAjax(gzGzsxZxblConfigService.toggleEnableHandle(bo.getId(), bo.getIsEnableHandle()));
    }

    /**
     * 获取申办材料选项列表
     *
     * @param category 材料类别（1-个人，2-企业）
     */
    @SaCheckPermission("basicdata:gzsxZxblConfig:query")
    @GetMapping("/materials")
    public R<List<GzGzsxZxblMaterialVo>> getMaterialOptions(@RequestParam(required = false) Long category) {
        // 构建查询条件
        GzGzsxZxblMaterialBo bo = new GzGzsxZxblMaterialBo();
        if (category != null) {
            bo.setCategory(category);
        }
        List<GzGzsxZxblMaterialVo> list = gzGzsxZxblMaterialService.queryList(bo);
        return R.ok(list);
    }
}
