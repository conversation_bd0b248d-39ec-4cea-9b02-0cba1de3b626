package com.gxgz.basicdata.service;

import com.gxgz.basicdata.domain.vo.GzGzsxZxblConfigVo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblConfigBo;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 在线办理配置Service接口
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface IGzGzsxZxblConfigService {

    /**
     * 查询在线办理配置
     *
     * @param id 主键
     * @return 在线办理配置
     */
    GzGzsxZxblConfigVo queryById(Long id);

    /**
     * 分页查询在线办理配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 在线办理配置分页列表
     */
    TableDataInfo<GzGzsxZxblConfigVo> queryPageList(GzGzsxZxblConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的在线办理配置列表
     *
     * @param bo 查询条件
     * @return 在线办理配置列表
     */
    List<GzGzsxZxblConfigVo> queryList(GzGzsxZxblConfigBo bo);

    /**
     * 新增在线办理配置
     *
     * @param bo 在线办理配置
     * @return 是否新增成功
     */
    Boolean insertByBo(GzGzsxZxblConfigBo bo);

    /**
     * 修改在线办理配置
     *
     * @param bo 在线办理配置
     * @return 是否修改成功
     */
    Boolean updateByBo(GzGzsxZxblConfigBo bo);

    /**
     * 校验并批量删除在线办理配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据公证事项ID查询在线办理配置
     *
     * @param gzsxId 公证事项ID
     * @return 在线办理配置
     */
    GzGzsxZxblConfigVo queryByGzsxId(Long gzsxId);

    /**
     * 批量保存在线办理配置
     *
     * @param boList 在线办理配置列表
     * @return 是否保存成功
     */
    Boolean batchSave(List<GzGzsxZxblConfigBo> boList);

    /**
     * 切换在线办理状态
     *
     * @param id              配置ID
     * @param isOnlineHandle  是否在线办理
     * @return 是否切换成功
     */
    Boolean toggleOnlineHandle(Long id, Long isOnlineHandle);

    /**
     * 切换启用办理状态
     *
     * @param id             配置ID
     * @param isEnableHandle 是否启用办理
     * @return 是否切换成功
     */
    Boolean toggleEnableHandle(Long id, Long isEnableHandle);
}
