package com.gxgz.guichain.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gxgz.common.core.domain.model.LoginUser;
import com.gxgz.common.core.exception.ServiceException;
import com.gxgz.common.core.utils.file.FileUtils;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.satoken.utils.LoginHelper;
import com.gxgz.guichain.componet.GxBaasComponent;
import com.gxgz.guichain.config.GxBaasConfig;
import com.gxgz.guichain.domain.GxbassChain;
import com.gxgz.guichain.domain.bo.GxBassChainBo;
//import com.gxgz.guichain.domain.bo.GxbassChainBo;
import com.gxgz.guichain.domain.bo.UploadChainFileBo;
import com.gxgz.guichain.domain.vo.GxDetailVo;
import com.gxgz.guichain.domain.vo.GxbassChainVo;
import com.gxgz.guichain.mapper.GxbassChainMapper;
import com.gxgz.guichain.service.IGxbassChainService;
import com.gxgz.guichain.utils.OnChainUtils;
import com.gxgz.system.domain.vo.SysOssVo;
import com.gxgz.system.service.ISysOssService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.rmi.ServerException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 桂链存证信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@RequiredArgsConstructor
@Service
public class GxbassChainServiceImpl implements IGxbassChainService {

    private final GxbassChainMapper baseMapper;
    private final GxBaasComponent gxBaasComponent;
    private final ISysOssService sysOssService;
    private final GxBaasConfig gxBaasConfig;
//    /**
//     * 查询桂链存证信息
//     */
//    @Override
//    public GxbassChainVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }
//
//    /**
//     * 查询桂链存证信息列表
//     */
//    @Override
//    public TableDataInfo<GxbassChainVo> queryPageList(GxbassChainBo bo, PageQuery pageQuery) {
////        LoginUser loginUser = LoginHelper.getLoginUser();
////        bo.setChainUserId(loginUser.getUserId());
//        LambdaQueryWrapper<GxbassChain> lqw = buildQueryWrapper(bo);
//        Page<GxbassChainVo> result = baseMapper.selectPageList(pageQuery.build(), lqw);
//        return TableDataInfo.build(result);
//    }
//
//    /**
//     * 查询桂链存证信息列表
//     */
//    @Override
//    public List<GxbassChainVo> queryList(GxbassChainBo bo) {
//        LambdaQueryWrapper<GxbassChain> lqw = buildQueryWrapper(bo);
//        return baseMapper.selectVoList(lqw);
//    }
//
//    private LambdaQueryWrapper<GxbassChain> buildQueryWrapper(GxbassChainBo bo) {
//        Map<String, Object> params = bo.getParams();
//        LambdaQueryWrapper<GxbassChain> lqw = Wrappers.lambdaQuery();
//        lqw.eq(StringUtils.isNotBlank(bo.getTxHash()), GxbassChain::getTxHash, bo.getTxHash());
//        lqw.eq(bo.getBlockHeight() != null, GxbassChain::getBlockHeight, bo.getBlockHeight());
//        lqw.eq(StringUtils.isNotBlank(bo.getBlockHash()), GxbassChain::getBlockHash, bo.getBlockHash());
//        lqw.eq(StringUtils.isNotBlank(bo.getSigner()), GxbassChain::getSigner, bo.getSigner());
//        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), GxbassChain::getBizId, bo.getBizId());
//        lqw.like(StringUtils.isNotBlank(bo.getTxTime()), GxbassChain::getTxTime, bo.getTxTime());
//        lqw.eq(StringUtils.isNotBlank(bo.getData()), GxbassChain::getData, bo.getData());
//        lqw.eq(StringUtils.isNotBlank(bo.getSignature()), GxbassChain::getSignature, bo.getSignature());
//        lqw.eq(StringUtils.isNotBlank(bo.getPublicKey()), GxbassChain::getPublicKey, bo.getPublicKey());
//        lqw.eq(bo.getEncryptMode() != null, GxbassChain::getEncryptMode, bo.getEncryptMode());
//        lqw.eq(bo.getProofType() != null, GxbassChain::getProofType, bo.getProofType());
//        lqw.like(StringUtils.isNotBlank(bo.getFileName()), GxbassChain::getFileName, bo.getFileName());
//        lqw.eq(StringUtils.isNotBlank(bo.getChannelId()), GxbassChain::getChannelId, bo.getChannelId());
//        lqw.eq(StringUtils.isNotBlank(bo.getIpfsPath()), GxbassChain::getIpfsPath, bo.getIpfsPath());
//        lqw.eq(StringUtils.isNotBlank(bo.getFromAddr()), GxbassChain::getFromAddr, bo.getFromAddr());
//        if (params.get("beginTime") != null && params.get("endTime") != null) {
//            lqw.between(GxbassChain::getTxTime, params.get("beginTime")+" 00:00:00", params.get("endTime")+" 23:59:59");
//        }
//
//        return lqw;
//    }
//
//    /**
//     * 新增桂链存证信息
//     */
//    @Override
//    public Boolean insertByBo(GxbassChainBo bo) {
//        GxbassChain add = BeanUtil.toBean(bo, GxbassChain.class);
//        validEntityBeforeSave(add);
//        boolean flag = baseMapper.insert(add) > 0;
//        if (flag) {
//            bo.setId(add.getId());
//        }
//        return flag;
//    }
//
//    /**
//     * 修改桂链存证信息
//     */
//    @Override
//    public Boolean updateByBo(GxbassChainBo bo) {
//        GxbassChain update = BeanUtil.toBean(bo, GxbassChain.class);
//        validEntityBeforeSave(update);
//        return baseMapper.updateById(update) > 0;
//    }
//
//    /**
//     * 保存前的数据校验
//     */
//    private void validEntityBeforeSave(GxbassChain entity) {
//        //TODO 做一些数据校验,如唯一约束
//    }
//
//    /**
//     * 批量删除桂链存证信息
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    /**
     * 桂链文件上链
     *
     * @param bo
     * @throws IOException
     */
    @Override
    @Async
    public void uploadFileChain(UploadChainFileBo bo) throws IOException {

        SysOssVo ossVo = sysOssService.getById(bo.getOssId());
        if (ObjectUtil.isNull(ossVo)) {
            throw new ServerException("文件上传失败，请重新上传");
        }

        // 创建 URL 对象
        URL url = new URL(ossVo.getUrl());
        // 创建临时文件，并保留后缀
        File tempfile = Files.createTempFile(ossVo.getOriginalName().split("\\.")[0], ossVo.getFileSuffix()).toFile();
        try (InputStream inputStream = url.openStream();
             OutputStream outputStream = new FileOutputStream(tempfile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        String result = gxBaasComponent.uploadFile(tempfile, bo.getChainDec());
        try {
            // 线程休眠2秒
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            // 处理异常
            e.printStackTrace();
        }
        tempfile.delete();
        // 数据上链日志
        OnChainUtils.addOnChainFileLog(bo.getOnChainClassifyEnum(), Long.valueOf(bo.getBizId()), ossVo.getOssId(), bo.getChainDec(), gxBaasConfig.getChannelId(),result);
//        return result;
    }

    /**
     * 桂链文件hash上链
     *
     * @param bo
     * @return
     */
    @Override
    @Async
    public void uploadData(GxBassChainBo bo) {
        String bizId = RandomUtil.randomNumbers(18);
        String result = gxBaasComponent.uploadData(bizId, bo.getAttrJSONStr(), bo.getChainDec());
        try {
            // 线程休眠2秒
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            // 处理异常
            e.printStackTrace();
        }
        HashMap map = new HashMap<>();
        map.put("txHash",result);
        // 数据上链日志
        OnChainUtils.addOnChainLog(bo.getOnChainClassifyEnum(), Long.valueOf(bo.getBizId()), bo.getAttrJSONStr(), bo.getChainDec(), gxBaasConfig.getChannelId(), result);
//        return map;
    }


    /**
     * 下载证书
     *
     * @param txHash
     * @param response
     */
    @Override
    public void downloadCertificate(String txHash, HttpServletResponse response) throws UnsupportedEncodingException {
        byte[] bytes = gxBaasComponent.downloadCertificate(txHash);
        FileUtils.setAttachmentResponseHeader(response, "桂链出证证书.pdf");
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            int available = inputStream.available();
            IoUtil.copy(inputStream, response.getOutputStream(), available);
            response.setContentLength(available);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 解密文件下载
     *
     * @param txHash
     * @param response
     */
    @Override
    public void downloadDecryptFile(String txHash, HttpServletResponse response) throws UnsupportedEncodingException {
        byte[] bytes = gxBaasComponent.getDecryptFile(txHash);
        LambdaQueryWrapper<GxbassChain> lqw = new LambdaQueryWrapper<>();
        lqw.eq(GxbassChain::getTxHash, txHash);
        GxbassChainVo gxbassChainVo = baseMapper.selectVoOne(lqw);
        SysOssVo ossVo = sysOssService.getById(gxbassChainVo.getOssId());
        FileUtils.setAttachmentResponseHeader(response, ossVo.getOriginalName());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            int available = inputStream.available();
            IoUtil.copy(inputStream, response.getOutputStream(), available);
            response.setContentLength(available);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String getFileSuffix(Long ossId) {
        SysOssVo ossVo = sysOssService.getById(ossId);
        if (ObjectUtil.isNull(ossVo)) {
            throw new ServiceException("文件不存在");
        }
        return ossVo.getFileSuffix();
    }

    @Override
    public GxDetailVo getChinInfo(String txHash) {
        GxDetailVo gxDetailVo = gxBaasComponent.getDetail(txHash);
        return gxDetailVo;
    }
}
