package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxdzbVo;
import com.gxgz.basicdata.domain.bo.GzGzsxdzbBo;
import com.gxgz.basicdata.service.IGzGzsxdzbService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 公证事项对照
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxdzb")
public class GzGzsxdzbController extends BaseController {

    private final IGzGzsxdzbService gzGzsxdzbService;

    /**
     * 查询公证事项对照列表
     */
//    @SaCheckPermission("basicdata:gzsxdzb:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxdzbVo> list(GzGzsxdzbBo bo, PageQuery pageQuery) {
        return gzGzsxdzbService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出公证事项对照列表
     */
//    @SaCheckPermission("basicdata:gzsxdzb:export")
    @Log(title = "公证事项对照", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxdzbBo bo, HttpServletResponse response) {
        List<GzGzsxdzbVo> list = gzGzsxdzbService.queryList(bo);
        ExcelUtil.exportExcel(list, "公证事项对照", GzGzsxdzbVo.class, response);
    }

    /**
     * 获取公证事项对照详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:gzsxdzb:query")
    @GetMapping("/{id}")
    public R<GzGzsxdzbVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxdzbService.queryById(id));
    }

    /**
     * 新增公证事项对照
     */
//    @SaCheckPermission("basicdata:gzsxdzb:add")
    @Log(title = "公证事项对照", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxdzbBo bo) {
        return toAjax(gzGzsxdzbService.insertByBo(bo));
    }

    /**
     * 修改公证事项对照
     */
//    @SaCheckPermission("basicdata:gzsxdzb:edit")
    @Log(title = "公证事项对照", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxdzbBo bo) {
        return toAjax(gzGzsxdzbService.updateByBo(bo));
    }

    /**
     * 删除公证事项对照
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:gzsxdzb:remove")
    @Log(title = "公证事项对照", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxdzbService.deleteWithValidByIds(List.of(ids), true));
    }
}
