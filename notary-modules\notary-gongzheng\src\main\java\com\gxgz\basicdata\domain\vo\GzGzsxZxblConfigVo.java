package com.gxgz.basicdata.domain.vo;

import com.gxgz.basicdata.domain.GzGzsxZxblConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gxgz.common.excel.annotation.ExcelDictFormat;
import com.gxgz.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 在线办理配置视图对象 gz_gzsx_zxbl_config
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GzGzsxZxblConfig.class)
public class GzGzsxZxblConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 公证事项名称（冗余字段）
     */
    @ExcelProperty(value = "公证事项名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "冗=余字段")
    private String gzsxName;

    /**
     * 事项编号（冗余字段）
     */
    @ExcelProperty(value = "事项编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "冗=余字段")
    private String gzsxCode;

    /**
     * 公证事项ID（基于GzsxVO中id）
     */
    @ExcelProperty(value = "公证事项ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "基=于GzsxVO中id")
    private Long gzsxId;

    /**
     * 是否在线办理（1是/0否）
     */
    @ExcelProperty(value = "是否在线办理", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=是/0否")
    private Long isOnlineHandle;

    /**
     * 是否启用办理（1是/0否）
     */
    @ExcelProperty(value = "是否启用办理", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=是/0否")
    private Long isEnableHandle;

    /**
     * 申办材料ID（多个以逗号分隔）
     */
    @ExcelProperty(value = "申办材料ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个以逗号分隔")
    private String materialIds;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
