package com.gxgz.basicdata.domain.bo;

import com.gxgz.basicdata.domain.GzGzsxZxblMaterial;
import com.gxgz.common.mybatis.core.domain.BaseEntity;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 在线办理材料管理业务对象 gz_gzsx_zxbl_material
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GzGzsxZxblMaterial.class, reverseConvertGenerate = false)
public class GzGzsxZxblMaterialBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 材料名称
     */
    @NotBlank(message = "材料名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String materialName;

    /**
     * 类别：1-个人，2-企业
     */
    @NotNull(message = "类别：1-个人，2-企业不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long category;

    /**
     * 说明
     */
    private String description;

    /**
     * 备注
     */
    private String remark;


}
