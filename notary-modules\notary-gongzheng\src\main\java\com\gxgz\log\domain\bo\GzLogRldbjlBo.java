package com.gxgz.log.domain.bo;

import com.gxgz.log.domain.GzLogRldbjl;
import com.gxgz.common.mybatis.core.domain.BaseEntity;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 日志-人脸对比记录业务对象 gz_log_rldbjl
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GzLogRldbjl.class, reverseConvertGenerate = false)
public class GzLogRldbjlBo extends BaseEntity {

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 对比人脸图片
     */
    private String dbrl;

    /**
     * 对比结果
     */
    private String dbjg;

    /**
     * 当事人ID
     */
    private Long dsrId;
    /**
     * 当事人姓名
     */
    private String dsrXm;
    /**
     * 对比指数
     */
    private String dbzs;
    /**
     * 对比日期
     */
    private Date dbrq;
    /**
     * 当事人身份证号
     */
    private String dsrZjhm;
}
