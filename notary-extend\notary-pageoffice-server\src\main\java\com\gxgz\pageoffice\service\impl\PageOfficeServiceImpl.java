package com.gxgz.pageoffice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gxgz.common.core.domain.dto.ParamsDTO;
import com.gxgz.common.core.enums.FileTypeEnum;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.pageoffice.config.PageOfficeConfig;
import com.gxgz.pageoffice.domain.bo.DocumentEditBO;
import com.gxgz.pageoffice.domain.bo.DocumentDeleteBO;
import com.gxgz.pageoffice.domain.bo.UnifiedDocumentBO;
import com.gxgz.pageoffice.domain.vo.DocumentEditVO;
import com.gxgz.pageoffice.domain.vo.UnifiedDocumentVO;
import com.gxgz.pageoffice.service.IPageOfficeService;
import com.zhuozhengsoft.pageoffice.OpenModeType;
import com.zhuozhengsoft.pageoffice.PageOfficeCtrl;
import com.zhuozhengsoft.pageoffice.word.DataRegionWriter;
import com.zhuozhengsoft.pageoffice.word.DataTagWriter;
import com.zhuozhengsoft.pageoffice.word.WordDocumentWriter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * PageOffice服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PageOfficeServiceImpl implements IPageOfficeService {

    private final PageOfficeConfig pageOfficeConfig;

    @Override
    public UnifiedDocumentVO processDocument(UnifiedDocumentBO unifiedDocumentBO, HttpServletRequest request) {
        try {
            // 验证参数
            if (StrUtil.isBlank(unifiedDocumentBO.getDocumentPath()) && StrUtil.isBlank(unifiedDocumentBO.getDocumentUrl())) {
                return buildErrorResponse("文档路径和文档URL不能同时为空", unifiedDocumentBO);
            }

            // 自动识别文档类型
            String documentType = detectDocumentType(unifiedDocumentBO);
            if (StrUtil.isBlank(documentType)) {
                return buildErrorResponse("无法识别文档类型", unifiedDocumentBO);
            }

            // 转换为DocumentEditBO
            DocumentEditBO documentEditBO = convertToDocumentEditBO(unifiedDocumentBO, documentType);

            // 处理自定义参数嵌入
            processCustomParams(documentEditBO, unifiedDocumentBO.getCustomParams());

            // 根据操作类型和文档类型调用相应方法
            DocumentEditVO editResult = processDocumentByType(documentEditBO, documentType, request);

            // 转换为统一返回对象
            return convertToUnifiedDocumentVO(editResult, unifiedDocumentBO, documentType);

        } catch (Exception e) {
            log.error("统一文档操作失败", e);
            return buildErrorResponse("统一文档操作失败: " + e.getMessage(), unifiedDocumentBO);
        }
    }

    @Override
    public Boolean deleteDocument(DocumentDeleteBO documentDeleteBO) {
        try {
            if (StrUtil.isNotBlank(documentDeleteBO.getDocumentId())) {
                return deleteDocumentById(documentDeleteBO.getDocumentId());
            } else if (StrUtil.isNotBlank(documentDeleteBO.getDocumentPath())) {
                return deleteDocumentByPath(documentDeleteBO.getDocumentPath());
            } else if (StrUtil.isNotBlank(documentDeleteBO.getDocumentUrl())) {
                // 网络文档删除逻辑（如果支持）
                log.warn("网络文档删除功能暂不支持: {}", documentDeleteBO.getDocumentUrl());
                return false;
            } else {
                log.error("删除文档参数不足：文档ID、路径、URL至少需要提供一个");
                return false;
            }
        } catch (Exception e) {
            log.error("删除文档失败", e);
            return false;
        }
    }

    @Override
    public Boolean deleteDocumentById(String documentId) {
        try {
            log.info("根据文档ID删除文档: {}", documentId);

            // 这里可以根据文档ID查找对应的文件路径
            // 假设文档ID对应的文件存储在临时目录或文档目录中
            String tempPath = pageOfficeConfig.getTempPath();
            String docPath = pageOfficeConfig.getDocBasePath();

            // 尝试删除临时文件
            boolean tempDeleted = deleteFileByPattern(tempPath, documentId);
            // 尝试删除文档文件
            boolean docDeleted = deleteFileByPattern(docPath, documentId);

            return tempDeleted || docDeleted;
        } catch (Exception e) {
            log.error("根据文档ID删除文档失败: {}", documentId, e);
            return false;
        }
    }

    @Override
    public Boolean deleteDocumentByPath(String documentPath) {
        try {
            log.info("根据文档路径删除文档: {}", documentPath);

            File file = new File(documentPath);
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("文档删除成功: {}", documentPath);

                    // 删除相关的备份文件和临时文件
                    deleteRelatedFiles(documentPath);

                    return true;
                } else {
                    log.error("文档删除失败: {}", documentPath);
                    return false;
                }
            } else {
                log.warn("文档不存在: {}", documentPath);
                return true; // 文件不存在也认为删除成功
            }
        } catch (Exception e) {
            log.error("根据文档路径删除文档失败: {}", documentPath, e);
            return false;
        }
    }

    /**
     * 检测文档类型
     */
    private String detectDocumentType(UnifiedDocumentBO unifiedDocumentBO) {
        // 如果已指定类型，直接返回
        if (StrUtil.isNotBlank(unifiedDocumentBO.getDocumentType())) {
            return unifiedDocumentBO.getDocumentType().toLowerCase();
        }

        // 根据文件名或URL检测类型
        String fileName = unifiedDocumentBO.getDocumentName();
        if (StrUtil.isBlank(fileName)) {
            fileName = StrUtil.isNotBlank(unifiedDocumentBO.getDocumentPath()) ?
                unifiedDocumentBO.getDocumentPath() : unifiedDocumentBO.getDocumentUrl();
        }

        if (StrUtil.isNotBlank(fileName)) {
            String extension = FileUtil.extName(fileName).toLowerCase();
            switch (extension) {
                case "doc":
                case "docx":
                    return "word";
                case "xls":
                case "xlsx":
                    return "excel";
                case "ppt":
                case "pptx":
                    return "ppt";
                case "pdf":
                    return "pdf";
                default:
                    return null;
            }
        }

        return null;
    }

    /**
     * 转换为DocumentEditBO
     */
    private DocumentEditBO convertToDocumentEditBO(UnifiedDocumentBO source, String documentType) {
        DocumentEditBO target = new DocumentEditBO();
        target.setDocumentId(source.getDocumentId());
        target.setDocumentName(source.getDocumentName());
        target.setDocumentPath(source.getDocumentPath());
        target.setDocumentUrl(source.getDocumentUrl());
        target.setDocumentType(documentType);

        // 根据action设置editMode
        String editMode = "edit";
        if ("view".equals(source.getAction())) {
            editMode = "read";
        } else if (StrUtil.isNotBlank(source.getEditMode())) {
            editMode = source.getEditMode();
        }
        target.setEditMode(editMode);

        target.setUserName(source.getUserName());
        target.setUserId(source.getUserId());
        target.setEnableRevision(source.getEnableRevision());
        target.setEnableSeal(source.getEnableSeal());
        target.setCallbackUrl(source.getCallbackUrl());
        target.setWidth(source.getWidth());
        target.setHeight(source.getHeight());
        target.setCustomParams(source.getCustomParams());
        target.setDescription(source.getDescription());
        target.setAutoSave(source.getAutoSave());
        target.setAutoSaveInterval(source.getAutoSaveInterval());
        target.setEnableCollaboration(source.getEnableCollaboration());

        return target;
    }

    /**
     * 处理自定义参数
     */
    private void processCustomParams(DocumentEditBO documentEditBO, Map<String, Object> customParams) {
        if (customParams != null && !customParams.isEmpty()) {
            // 这里可以实现参数嵌入逻辑
            // 例如：替换文档内容中的占位符等
            log.info("处理自定义参数: {}", customParams);
            documentEditBO.setCustomParams(customParams);
        }
    }

    /**
     * 根据文档类型处理文档
     */
    private DocumentEditVO processDocumentByType(DocumentEditBO documentEditBO, String documentType, HttpServletRequest request) {
        switch (documentType.toLowerCase()) {
            case "word":
                return openWordDocument(documentEditBO, request);
            case "excel":
                return openExcelDocument(documentEditBO, request);
            case "ppt":
            case "powerpoint":
                return openPowerPointDocument(documentEditBO, request);
            case "pdf":
                return openPdfDocument(documentEditBO, request);
            default:
                return DocumentEditVO.builder()
                    .code(400)
                    .message("不支持的文档类型: " + documentType)
                    .build();
        }
    }

    /**
     * 转换为统一返回对象
     */
    private UnifiedDocumentVO convertToUnifiedDocumentVO(DocumentEditVO source, UnifiedDocumentBO request, String documentType) {
        Map<String, Object> extendedInfo = new HashMap<>();
        if (request.getExtendedProperties() != null) {
            extendedInfo.putAll(request.getExtendedProperties());
        }

        return UnifiedDocumentVO.builder()
            .savePath(request.getSavePath())
            .callbackUrl(request.getCallbackUrl())
            .ywTyp(request.getYwTyp())
            .documentId(source.getDocumentId())
            .documentName(request.getDocumentName())
            .documentType(documentType)
            .action(request.getAction())
            .editMode(request.getEditMode())
            .htmlCode(source.getHtmlCode())
            .controlId(source.getControlId())
            .editUrl(source.getEditUrl())
            .saveUrl(source.getSaveUrl())
            .deleteUrl(buildDeleteUrl(source.getDocumentId()))
            .jsCode(source.getJsCode())
            .status("active")
            .documentPath(request.getDocumentPath())
            .documentUrl(request.getDocumentUrl())
            .createTime(LocalDateTime.now())
            .userName(request.getUserName())
            .userId(request.getUserId())
            .enableSeal(request.getEnableSeal())
            .enableRevision(request.getEnableRevision())
            .code(source.getCode())
            .message(source.getMessage())
            .extendedInfo(extendedInfo)
            .build();
    }

    /**
     * 构建错误响应
     */
    private UnifiedDocumentVO buildErrorResponse(String message, UnifiedDocumentBO request) {
        return UnifiedDocumentVO.builder()
            .documentId(request.getDocumentId())
            .documentName(request.getDocumentName())
            .action(request.getAction())
            .code(500)
            .message(message)
            .createTime(LocalDateTime.now())
            .build();
    }

    /**
     * 构建删除URL
     */
    private String buildDeleteUrl(String documentId) {
        return "/api/unified-document/delete/" + documentId;
    }

    /**
     * 根据模式删除文件
     */
    private boolean deleteFileByPattern(String basePath, String documentId) {
        try {
            File baseDir = new File(basePath);
            if (!baseDir.exists() || !baseDir.isDirectory()) {
                return false;
            }

            File[] files = baseDir.listFiles(file -> file.getName().contains(documentId));
            if (files != null) {
                boolean anyDeleted = false;
                for (File file : files) {
                    if (file.delete()) {
                        log.info("删除文件成功: {}", file.getAbsolutePath());
                        anyDeleted = true;
                    } else {
                        log.warn("删除文件失败: {}", file.getAbsolutePath());
                    }
                }
                return anyDeleted;
            }
            return false;
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    /**
     * 删除相关文件
     */
    private void deleteRelatedFiles(String documentPath) {
        try {
            // 删除备份文件
            String backupPath = documentPath + ".bak";
            File backupFile = new File(backupPath);
            if (backupFile.exists()) {
                backupFile.delete();
                log.info("删除备份文件: {}", backupPath);
            }

            // 删除临时文件
            String fileName = FileUtil.getName(documentPath);
            String tempPath = pageOfficeConfig.getTempPath();
            deleteFileByPattern(tempPath, fileName);
        } catch (Exception e) {
            log.error("删除相关文件失败", e);
        }
    }

    @Override
    public DocumentEditVO createDocumentEditControl(DocumentEditBO documentEditBO, HttpServletRequest request) {
        try {
            // 根据文档类型调用对应的处理方法
            switch (documentEditBO.getDocumentType().toLowerCase()) {
                case "word":
                case "doc":
                case "docx":
                    return openWordDocument(documentEditBO, request);
                case "excel":
                case "xls":
                case "xlsx":
                    return openExcelDocument(documentEditBO, request);
                case "ppt":
                case "pptx":
                case "powerpoint":
                    return openPowerPointDocument(documentEditBO, request);
                case "pdf":
                    return openPdfDocument(documentEditBO, request);
                default:
                    return DocumentEditVO.builder()
                        .code(400)
                        .message("不支持的文档类型: " + documentEditBO.getDocumentType())
                        .build();
            }
        } catch (Exception e) {
            log.error("创建文档编辑控件失败", e);
            return DocumentEditVO.builder()
                .code(500)
                .message("创建文档编辑控件失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public DocumentEditVO openWordDocument(DocumentEditBO documentEditBO, HttpServletRequest request) {
        PageOfficeCtrl poCtrl = new PageOfficeCtrl(request);
        String controlId = "PageOfficeCtrl_" + UUID.randomUUID().toString().replace("-", "");

        // 设置服务页面
        poCtrl.setServerPage(request.getContextPath() + "/poserver.zz");

        // 根据编辑模式设置打开方式
        OpenModeType openMode = getOpenModeByEditMode(documentEditBO.getEditMode());

        // 配置工具按钮
        setupToolButtons(poCtrl, documentEditBO);

        // 设置保存页面
        if (documentEditBO.getCallbackUrl() != null) {
            poCtrl.setSaveFilePage(documentEditBO.getCallbackUrl());
        } else {
            poCtrl.setSaveFilePage("/api/unified-document/save");
        }

        // 启用修订模式
        if (Boolean.TRUE.equals(documentEditBO.getEnableRevision())) {
//            poCtrl.setTrackRevisions(1);
        }

        // 启用印章功能
        if (Boolean.TRUE.equals(documentEditBO.getEnableSeal()) && pageOfficeConfig.getEnableSeal()) {
            poCtrl.addCustomToolButton("盖章", "POSeal", 2);
        }

        if (CollUtil.isNotEmpty(documentEditBO.getCustomParams())) {
            //替换变量
            WordDocumentWriter wd = new WordDocumentWriter();
            documentEditBO.getCustomParams().forEach((key, value) -> {
                ObjectMapper mapper = new ObjectMapper();
                ParamsDTO paramsDTO = mapper.convertValue(value, ParamsDTO.class);
                if (StringUtils.isNotBlank(paramsDTO.getValue())) {
                    if (paramsDTO.getType() == FileTypeEnum.TEXT.getCode()) {
                        //插入文本
                        DataTagWriter dateTag = wd.openDataTag("{" + key + "}");
                        dateTag.setValue(paramsDTO.getValue());
                    } else if (paramsDTO.getType() == FileTypeEnum.IMG.getCode()) {
                        //插入图片
                        if (paramsDTO.getValue().toString().indexOf(",") > 0) {
                            String[] imgs = paramsDTO.getValue().toString().split(",");
                            for (String img : imgs) {
                                DataRegionWriter dataRegion1 = wd.openDataRegion("PO_" + key);
                                dataRegion1.setValue("[image]" + img + "[/image][PO_BR]");
                            }
                        } else {
                            DataRegionWriter dataRegion1 = wd.openDataRegion("PO_" + key);
                            dataRegion1.setValue("[image]" + paramsDTO.getValue().toString() + "[/image][PO_BR]");
                        }
                    } else if (paramsDTO.getType() == FileTypeEnum.EXCEL.getCode()) {
                        //标签的参数地址可以是“磁盘路径”，也可以是“文档url下载地址（不支持跨域）”
                        DataRegionWriter dataReg = wd.openDataRegion("PO_" + key);
                        dataReg.setValue("[excel]" + paramsDTO.getValue() + "[/excel]");
                    } else if (paramsDTO.getType() == FileTypeEnum.WORD.getCode()) {
                        DataRegionWriter dataReg = wd.openDataRegion("PO_" + key);
                        //标签的参数地址可以是“磁盘路径”，也可以是“文档url下载地址（不支持跨域）”
                        dataReg.setValue("[word]" + paramsDTO.getValue() + "[/word]");
                    }
                }
            });
            poCtrl.setWriter(wd); //必须
        }
        // 打开文档
        poCtrl.webOpen(documentEditBO.getDocumentPath(), openMode, documentEditBO.getUserName());

        String htmlCode = poCtrl.getHtmlCode(controlId);

        return DocumentEditVO.builder()
            .documentId(documentEditBO.getDocumentId())
            .htmlCode(htmlCode)
            .controlId(controlId)
            .editUrl(request.getRequestURL().toString())
            .saveUrl(documentEditBO.getCallbackUrl())
            .code(200)
            .message("Word文档加载成功")
            .build();
    }

    @Override
    public DocumentEditVO openExcelDocument(DocumentEditBO documentEditBO, HttpServletRequest request) {
        PageOfficeCtrl poCtrl = new PageOfficeCtrl(request);
        String controlId = "PageOfficeCtrl_" + UUID.randomUUID().toString().replace("-", "");

        // 设置服务页面
        poCtrl.setServerPage(request.getContextPath() + "/poserver.zz");

        // 根据编辑模式设置打开方式
        OpenModeType openMode = getExcelOpenModeByEditMode(documentEditBO.getEditMode());

        // 配置工具按钮
        setupToolButtons(poCtrl, documentEditBO);

        // 设置保存页面
        if (documentEditBO.getCallbackUrl() != null) {
            poCtrl.setSaveFilePage(documentEditBO.getCallbackUrl());
        } else {
            poCtrl.setSaveFilePage("/api/unified-document/save");
        }

        // 打开文档
        poCtrl.webOpen(documentEditBO.getDocumentPath(), openMode, documentEditBO.getUserName());

        String htmlCode = poCtrl.getHtmlCode(controlId);

        return DocumentEditVO.builder()
            .documentId(documentEditBO.getDocumentId())
            .htmlCode(htmlCode)
            .controlId(controlId)
            .editUrl(request.getRequestURL().toString())
            .saveUrl("/api/unified-document/save")
            .code(200)
            .message("Excel文档加载成功")
            .build();
    }

    @Override
    public DocumentEditVO openPowerPointDocument(DocumentEditBO documentEditBO, HttpServletRequest request) {
        PageOfficeCtrl poCtrl = new PageOfficeCtrl(request);
        String controlId = "PageOfficeCtrl_" + UUID.randomUUID().toString().replace("-", "");

        // 设置服务页面
        poCtrl.setServerPage(request.getContextPath() + "/poserver.zz");

        // 根据编辑模式设置打开方式
        OpenModeType openMode = getPowerPointOpenModeByEditMode(documentEditBO.getEditMode());

        // 配置工具按钮
        setupToolButtons(poCtrl, documentEditBO);

        // 设置保存页面
        if (documentEditBO.getCallbackUrl() != null) {
            poCtrl.setSaveFilePage(documentEditBO.getCallbackUrl());
        } else {
            poCtrl.setSaveFilePage("/api/unified-document/save");
        }

        // 打开文档
        poCtrl.webOpen(documentEditBO.getDocumentPath(), openMode, documentEditBO.getUserName());

        String htmlCode = poCtrl.getHtmlCode(controlId);

        return DocumentEditVO.builder()
            .documentId(documentEditBO.getDocumentId())
            .htmlCode(htmlCode)
            .controlId(controlId)
            .editUrl(request.getRequestURL().toString())
            .saveUrl("/api/unified-document/save")
            .code(200)
            .message("PowerPoint文档加载成功")
            .build();
    }

    @Override
    public DocumentEditVO openPdfDocument(DocumentEditBO documentEditBO, HttpServletRequest request) {
        PageOfficeCtrl poCtrl = new PageOfficeCtrl(request);
        String controlId = "PageOfficeCtrl_" + UUID.randomUUID().toString().replace("-", "");

        // 设置服务页面
        poCtrl.setServerPage(request.getContextPath() + "/poserver.zz");

        // PDF只支持只读模式
        OpenModeType openMode = OpenModeType.docAdmin;

        // 配置基础工具按钮
        poCtrl.addCustomToolButton("打印", "PrintFile", 6);
        poCtrl.addCustomToolButton("关闭", "Close", 21);

        // 打开文档
        poCtrl.webOpen(documentEditBO.getDocumentPath(), openMode, documentEditBO.getUserName());

        String htmlCode = poCtrl.getHtmlCode(controlId);

        return DocumentEditVO.builder()
            .documentId(documentEditBO.getDocumentId())
            .htmlCode(htmlCode)
            .controlId(controlId)
            .editUrl(request.getRequestURL().toString())
            .code(200)
            .message("PDF文档加载成功")
            .build();
    }

    @Override
    public Boolean saveDocument(String documentId, HttpServletRequest request) {
        try {
            // 这里实现文档保存逻辑
            log.info("保存文档: {}", documentId);
            return true;
        } catch (Exception e) {
            log.error("保存文档失败: {}", documentId, e);
            return false;
        }
    }

    @Override
    public Boolean closeDocument(String documentId) {
        try {
            log.info("关闭文档: {}", documentId);
            return true;
        } catch (Exception e) {
            log.error("关闭文档失败: {}", documentId, e);
            return false;
        }
    }

    @Override
    public String getDocumentStatus(String documentId) {
        // 这里可以实现获取文档状态的逻辑
        return "active";
    }

    /**
     * 根据编辑模式获取Word文档打开方式
     */
    private OpenModeType getOpenModeByEditMode(String editMode) {
        switch (editMode.toLowerCase()) {
            case "read":
                return OpenModeType.docReadOnly;
            case "review":
                return OpenModeType.docRevisionOnly;
            case "edit":
            default:
                return OpenModeType.docNormalEdit;
        }
    }

    /**
     * 根据编辑模式获取Excel文档打开方式
     */
    private OpenModeType getExcelOpenModeByEditMode(String editMode) {
        switch (editMode.toLowerCase()) {
            case "read":
                return OpenModeType.xlsReadOnly;
            case "edit":
            default:
                return OpenModeType.xlsNormalEdit;
        }
    }

    /**
     * 根据编辑模式获取PowerPoint文档打开方式
     */
    private OpenModeType getPowerPointOpenModeByEditMode(String editMode) {
        switch (editMode.toLowerCase()) {
            case "read":
                return OpenModeType.pptReadOnly;
            case "edit":
            default:
                return OpenModeType.pptNormalEdit;
        }
    }

    /**
     * 设置工具按钮
     */
    private void setupToolButtons(PageOfficeCtrl poCtrl, DocumentEditBO documentEditBO) {
        if (!"read".equals(documentEditBO.getEditMode())) {
            poCtrl.addCustomToolButton("保存", "Save", 1);
            poCtrl.addCustomToolButton("另存为", "SaveAs", 12);
        }

        poCtrl.addCustomToolButton("打印设置", "PrintSet", 0);
        poCtrl.addCustomToolButton("打印", "PrintFile", 6);
        poCtrl.addCustomToolButton("全屏/还原", "IsFullScreen", 4);
        poCtrl.addCustomToolButton("-", "", 0);
        poCtrl.addCustomToolButton("关闭", "Close", 21);
    }

}
