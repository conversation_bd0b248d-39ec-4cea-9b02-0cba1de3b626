package com.gxgz.system.domain;

import com.gxgz.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 短信发送日志对象 gz_sms_record
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gz_sms_record")
public class GzSmsRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 公证卷宗ID
     */
    private Long gzjzId;

    /**
     * 当事人ID
     */
    private String dsrId;

    /**
     * 当事人姓名
     */
    private String dsrName;

    /**
     * 当事人联系电话
     */
    private String dsrPhone;

    /**
     * 短信内容
     */
    private String smsContent;

    /**
     * 发送时间(精确到秒)
     */
    private Date sendTime;

    /**
     * 发送状态
     */
    private Long sendStatus;

    /**
     * 发送反馈时间(精确到秒)
     */
    private Date feedbackTime;

    /**
     * 发送反馈结果(失败原因等)
     */
    private String feedbackResult;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 删除标识(0未删除，1已删除)
     */
    @TableLogic
    private String delFlag;

    private String gzjzBh;
}
