package com.gxgz.system.service.impl;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.core.utils.MapstructUtils;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.gxgz.system.domain.bo.GzSmsRecordBo;
import com.gxgz.system.domain.vo.GzSmsRecordVo;
import com.gxgz.system.domain.GzSmsRecord;
import com.gxgz.system.mapper.GzSmsRecordMapper;
import com.gxgz.system.service.IGzSmsRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 短信发送日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@RequiredArgsConstructor
@Service
public class GzSmsRecordServiceImpl implements IGzSmsRecordService {

    private final GzSmsRecordMapper baseMapper;

    /**
     * 查询短信发送日志
     *
     * @param id 主键
     * @return 短信发送日志
     */
    @Override
    public GzSmsRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询短信发送日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 短信发送日志分页列表
     */
    @Override
    public TableDataInfo<GzSmsRecordVo> queryPageList(GzSmsRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GzSmsRecord> lqw = buildQueryWrapper(bo);
        Page<GzSmsRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的短信发送日志列表
     *
     * @param bo 查询条件
     * @return 短信发送日志列表
     */
    @Override
    public List<GzSmsRecordVo> queryList(GzSmsRecordBo bo) {
        LambdaQueryWrapper<GzSmsRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GzSmsRecord> buildQueryWrapper(GzSmsRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GzSmsRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GzSmsRecord::getId);
        lqw.eq(bo.getGzjzId() != null, GzSmsRecord::getGzjzId, bo.getGzjzId());
        lqw.eq(StringUtils.isNotBlank(bo.getDsrId()), GzSmsRecord::getDsrId, bo.getDsrId());
        lqw.like(StringUtils.isNotBlank(bo.getDsrName()), GzSmsRecord::getDsrName, bo.getDsrName());
        lqw.like(StringUtils.isNotBlank(bo.getGzjzBh()), GzSmsRecord::getGzjzBh, bo.getGzjzBh());
        lqw.like(StringUtils.isNotBlank(bo.getDsrPhone()), GzSmsRecord::getDsrPhone, bo.getDsrPhone());
        lqw.like(StringUtils.isNotBlank(bo.getSmsContent()), GzSmsRecord::getSmsContent, bo.getSmsContent());
        lqw.eq(bo.getSendTime() != null, GzSmsRecord::getSendTime, bo.getSendTime());
        lqw.eq(bo.getSendStatus() != null, GzSmsRecord::getSendStatus, bo.getSendStatus());
        lqw.eq(bo.getFeedbackTime() != null, GzSmsRecord::getFeedbackTime, bo.getFeedbackTime());
        lqw.like(StringUtils.isNotBlank(bo.getFeedbackResult()), GzSmsRecord::getFeedbackResult, bo.getFeedbackResult());
        return lqw;
    }

    /**
     * 新增短信发送日志
     *
     * @param bo 短信发送日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GzSmsRecordBo bo) {
        GzSmsRecord add = MapstructUtils.convert(bo, GzSmsRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改短信发送日志
     *
     * @param bo 短信发送日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GzSmsRecordBo bo) {
        GzSmsRecord update = MapstructUtils.convert(bo, GzSmsRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GzSmsRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除短信发送日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
