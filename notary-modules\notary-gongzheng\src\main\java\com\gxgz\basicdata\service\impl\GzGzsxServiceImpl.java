package com.gxgz.basicdata.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gxgz.basicdata.domain.GzGzsxPz;
import com.gxgz.basicdata.domain.bo.GzGzsxPzBo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblBo;
import com.gxgz.basicdata.domain.vo.GzGzsxPzVo;
import com.gxgz.common.core.utils.MapstructUtils;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.gxgz.basicdata.domain.bo.GzGzsxBo;
import com.gxgz.basicdata.domain.vo.GzGzsxVo;
import com.gxgz.basicdata.domain.GzGzsx;
import com.gxgz.basicdata.mapper.GzGzsxMapper;
import com.gxgz.basicdata.service.IGzGzsxService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 基础数据-公证事项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RequiredArgsConstructor
@Service
public class GzGzsxServiceImpl implements IGzGzsxService {

    private final GzGzsxMapper baseMapper;

    /**
     * 查询基础数据-公证事项
     *
     * @param id 主键
     * @return 基础数据-公证事项
     */
    @Override
    public GzGzsxVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询基础数据-公证事项列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 基础数据-公证事项分页列表
     */
    @Override
    public TableDataInfo<GzGzsxVo> queryPageList(GzGzsxBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GzGzsx> lqw = buildQueryWrapper(bo);
        Page<GzGzsxVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的基础数据-公证事项列表
     *
     * @param bo 查询条件
     * @return 基础数据-公证事项列表
     */
    @Override
    public List<GzGzsxVo> queryList(GzGzsxBo bo) {
        LambdaQueryWrapper<GzGzsx> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GzGzsx> buildQueryWrapper(GzGzsxBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GzGzsx> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GzGzsx::getId);
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), GzGzsx::getTitle, bo.getTitle());
        lqw.eq(bo.getParentId() != null, GzGzsx::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getParentCode()), GzGzsx::getParentCode, bo.getParentCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), GzGzsx::getCode, bo.getCode());
        lqw.eq(bo.getLevel() != null, GzGzsx::getLevel, bo.getLevel());
        return lqw;
    }

    /**
     * 新增基础数据-公证事项
     *
     * @param bo 基础数据-公证事项
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GzGzsxBo bo) {
        GzGzsx add = MapstructUtils.convert(bo, GzGzsx.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改基础数据-公证事项
     *
     * @param bo 基础数据-公证事项
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GzGzsxBo bo) {
        GzGzsx update = MapstructUtils.convert(bo, GzGzsx.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GzGzsx entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除基础数据-公证事项信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<GzGzsxVo> listTree(GzGzsxBo bo) {
        LambdaQueryWrapper<GzGzsx> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<GzGzsxVo> listExclude(Long id) {
        List<GzGzsxVo> gzGzsxVoList = baseMapper.selectVoList(new LambdaQueryWrapper<GzGzsx>());
        gzGzsxVoList.removeIf(d -> d.getId().equals(id)
            || StringUtils.splitList(d.getAncestors()).contains(Convert.toStr(id)));
        return gzGzsxVoList;
    }

    @Override
    public List<GzGzsxVo> listTreeByZxbl(GzGzsxZxblBo bo) {
        LambdaQueryWrapper<GzGzsx> lqw = buildQueryWrapper2(bo);
        return baseMapper.selectVoList(lqw);
    }
    private LambdaQueryWrapper<GzGzsx> buildQueryWrapper2(GzGzsxZxblBo bo) {
        LambdaQueryWrapper<GzGzsx> lqw = Wrappers.lambdaQuery();
        // 1. 排序（固定逻辑，直接添加）
        lqw.orderByAsc(GzGzsx::getId);

        // 2. 标题模糊查询（判空，避免无效 LIKE）
        if (StringUtils.isNotBlank(bo.getTitle())) {
            lqw.like(GzGzsx::getTitle, bo.getTitle());
        }

        // 3. 子查询条件处理
        Long isEnableHandle = bo.getIsEnableHandle();
        Long isOnlineHandle = bo.getIsOnlineHandle();

        // 仅当至少一个参数有值时，才添加子查询（避免无意义的 WHERE 1=1）
        if (isEnableHandle != null || isOnlineHandle != null) {
            // 构建子查询的 WHERE 条件（动态拼接 OR）
            StringBuilder subWhere = new StringBuilder();
            // 存储子查询的参数（避免 SQL 注入，使用 MyBatis 的 ? 占位符）
            List<Object> subParams = new ArrayList<>();

            // 处理 isEnableHandle（不为空则添加条件）
            if (isEnableHandle != null) {
                subWhere.append("is_enable_handle="+isEnableHandle);
            }

            // 处理 isOnlineHandle（不为空则添加 OR 条件）
            if (isOnlineHandle != null) {
                if (subWhere.length() > 0) { // 若已有条件，先加 OR
                    subWhere.append(" OR ");
                }
                subWhere.append("is_online_handle="+isOnlineHandle);
            }

            // 拼接完整子查询，并绑定参数（关键：用 apply 传递 SQL 片段 + 参数）
            String subQuery = " id IN (SELECT gzsx_id FROM gz_gzsx_zxbl_config WHERE " + subWhere + ")";
            lqw.apply(subQuery);
        }

        return lqw;
    }
}
