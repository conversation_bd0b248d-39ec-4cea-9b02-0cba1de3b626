package com.gxgz.system.service;

import com.gxgz.system.domain.vo.GzSmsRecordVo;
import com.gxgz.system.domain.bo.GzSmsRecordBo;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 短信发送日志Service接口
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IGzSmsRecordService {

    /**
     * 查询短信发送日志
     *
     * @param id 主键
     * @return 短信发送日志
     */
    GzSmsRecordVo queryById(Long id);

    /**
     * 分页查询短信发送日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 短信发送日志分页列表
     */
    TableDataInfo<GzSmsRecordVo> queryPageList(GzSmsRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的短信发送日志列表
     *
     * @param bo 查询条件
     * @return 短信发送日志列表
     */
    List<GzSmsRecordVo> queryList(GzSmsRecordBo bo);

    /**
     * 新增短信发送日志
     *
     * @param bo 短信发送日志
     * @return 是否新增成功
     */
    Boolean insertByBo(GzSmsRecordBo bo);

    /**
     * 修改短信发送日志
     *
     * @param bo 短信发送日志
     * @return 是否修改成功
     */
    Boolean updateByBo(GzSmsRecordBo bo);

    /**
     * 校验并批量删除短信发送日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
