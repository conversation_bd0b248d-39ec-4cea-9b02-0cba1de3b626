package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsVo;
import com.gxgz.basicdata.domain.bo.GzGzsBo;
import com.gxgz.basicdata.service.IGzGzsService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 告知树
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzs")
public class GzGzsController extends BaseController {

    private final IGzGzsService gzGzsService;

    /**
     * 查询告知树列表
     */
//    @SaCheckPermission("basicdata:gzs:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsVo> list(GzGzsBo bo, PageQuery pageQuery) {
        return gzGzsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出告知树列表
     */
//    @SaCheckPermission("basicdata:gzs:export")
    @Log(title = "告知树", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsBo bo, HttpServletResponse response) {
        List<GzGzsVo> list = gzGzsService.queryList(bo);
        ExcelUtil.exportExcel(list, "告知树", GzGzsVo.class, response);
    }

    /**
     * 获取告知树详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:gzs:query")
    @GetMapping("/{id}")
    public R<GzGzsVo> getInfo(@NotNull(message = "主键不能为空")
                              @PathVariable Long id) {
        return R.ok(gzGzsService.queryById(id));
    }

    /**
     * 新增告知树
     */
//    @SaCheckPermission("basicdata:gzs:add")
    @Log(title = "告知树", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsBo bo) {
        return toAjax(gzGzsService.insertByBo(bo));
    }

    /**
     * 修改告知树
     */
//    @SaCheckPermission("basicdata:gzs:edit")
    @Log(title = "告知树", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsBo bo) {
        return toAjax(gzGzsService.updateByBo(bo));
    }

    /**
     * 删除告知树
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:gzs:remove")
    @Log(title = "告知树", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取树形结构
     *
     * @param bo
     * @return
     */
//    @SaCheckPermission("basicdata:gzs:list")
    @GetMapping("/listTree")
    public R tree(GzGzsBo bo) {
        return R.ok("ok", gzGzsService.getTree(bo));
    }


//    @SaCheckPermission("basicdata:gzs:list")
    @GetMapping("/list/exclude/{gzsId}")
    public R listExclude(@PathVariable(value = "gzsId", required = false) Long gzsId) {
        List<GzGzsVo> gzGzsVoList = gzGzsService.listExclude(gzsId);
        return R.ok("ok", gzGzsVoList);
    }
}
