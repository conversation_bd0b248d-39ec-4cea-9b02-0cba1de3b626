package com.gxgz.guichain.domain;

import com.gxgz.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 公证-桂链-上链日志对象 gz_gxbass_log
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gz_gxbass_log")
public class GzGxbassLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 上链描述
     */
    private String chainDec;

    /**
     * 上链数据
     */
    private String chainData;

    /**
     * 上链文件
     */
    private Long ossId;

    /**
     * 存证类型 1文件存证 2 数据存证
     */
    private Long chainType;

    /**
     * 虚拟通道ID，非虚拟通道加密方式返回空
     */
    private String channelId;

    /**
     * 加密方式
     */
    private Long encryptMode;

    /**
     * 交易哈希
     */
    private String txHash;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 业务分类
     */
    private String bizClassify;


}
