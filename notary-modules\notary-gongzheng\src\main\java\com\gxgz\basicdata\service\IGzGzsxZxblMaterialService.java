package com.gxgz.basicdata.service;

import com.gxgz.basicdata.domain.vo.GzGzsxZxblMaterialVo;
import com.gxgz.basicdata.domain.bo.GzGzsxZxblMaterialBo;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 在线办理材料管理Service接口
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface IGzGzsxZxblMaterialService {

    /**
     * 查询在线办理材料管理
     *
     * @param id 主键
     * @return 在线办理材料管理
     */
    GzGzsxZxblMaterialVo queryById(Long id);

    /**
     * 分页查询在线办理材料管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 在线办理材料管理分页列表
     */
    TableDataInfo<GzGzsxZxblMaterialVo> queryPageList(GzGzsxZxblMaterialBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的在线办理材料管理列表
     *
     * @param bo 查询条件
     * @return 在线办理材料管理列表
     */
    List<GzGzsxZxblMaterialVo> queryList(GzGzsxZxblMaterialBo bo);

    /**
     * 新增在线办理材料管理
     *
     * @param bo 在线办理材料管理
     * @return 是否新增成功
     */
    Boolean insertByBo(GzGzsxZxblMaterialBo bo);

    /**
     * 修改在线办理材料管理
     *
     * @param bo 在线办理材料管理
     * @return 是否修改成功
     */
    Boolean updateByBo(GzGzsxZxblMaterialBo bo);

    /**
     * 校验并批量删除在线办理材料管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
