package com.gxgz.basicdata.domain.bo;

import com.gxgz.basicdata.domain.GzGzsxZxblConfig;
import com.gxgz.common.mybatis.core.domain.BaseEntity;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 在线办理配置业务对象 gz_gzsx_zxbl_config
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GzGzsxZxblConfig.class, reverseConvertGenerate = false)
public class GzGzsxZxblConfigBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 公证事项名称（冗余字段）
     */
    @NotBlank(message = "公证事项名称（冗余字段）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gzsxName;

    /**
     * 事项编号（冗余字段）
     */
    @NotBlank(message = "事项编号（冗余字段）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gzsxCode;

    /**
     * 公证事项ID（基于GzsxVO中id）
     */
    @NotNull(message = "公证事项ID（基于GzsxVO中id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long gzsxId;

    /**
     * 是否在线办理（1是/0否）
     */
    @NotNull(message = "是否在线办理（1是/0否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isOnlineHandle;

    /**
     * 是否启用办理（1是/0否）
     */
    @NotNull(message = "是否启用办理（1是/0否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isEnableHandle;

    /**
     * 申办材料ID（多个以逗号分隔）
     */
    private String materialIds;

    /**
     * 备注
     */
    private String remark;


}
