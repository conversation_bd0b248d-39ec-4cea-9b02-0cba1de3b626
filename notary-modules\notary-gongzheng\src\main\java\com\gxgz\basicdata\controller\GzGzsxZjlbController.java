package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxZjlbVo;
import com.gxgz.basicdata.domain.bo.GzGzsxZjlbBo;
import com.gxgz.basicdata.service.IGzGzsxZjlbService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 证据列表
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxZjlb")
public class GzGzsxZjlbController extends BaseController {

    private final IGzGzsxZjlbService gzGzsxZjlbService;

    /**
     * 查询证据列表列表
     */
//    @SaCheckPermission("basicdata:gzsxZjlb:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxZjlbVo> list(GzGzsxZjlbBo bo, PageQuery pageQuery) {
        return gzGzsxZjlbService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出证据列表列表
     */
//    @SaCheckPermission("basicdata:gzsxZjlb:export")
    @Log(title = "证据列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxZjlbBo bo, HttpServletResponse response) {
        List<GzGzsxZjlbVo> list = gzGzsxZjlbService.queryList(bo);
        ExcelUtil.exportExcel(list, "证据列表", GzGzsxZjlbVo.class, response);
    }

    /**
     * 获取证据列表详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:gzsxZjlb:query")
    @GetMapping("/{id}")
    public R<GzGzsxZjlbVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxZjlbService.queryById(id));
    }

    /**
     * 新增证据列表
     */
//    @SaCheckPermission("basicdata:gzsxZjlb:add")
    @Log(title = "证据列表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxZjlbBo bo) {
        return toAjax(gzGzsxZjlbService.insertByBo(bo));
    }

    /**
     * 修改证据列表
     */
//    @SaCheckPermission("basicdata:gzsxZjlb:edit")
    @Log(title = "证据列表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxZjlbBo bo) {
        return toAjax(gzGzsxZjlbService.updateByBo(bo));
    }

    /**
     * 删除证据列表
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:gzsxZjlb:remove")
    @Log(title = "证据列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxZjlbService.deleteWithValidByIds(List.of(ids), true));
    }
}
