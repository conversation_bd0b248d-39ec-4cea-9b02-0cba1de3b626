package com.gxgz.common.core.enums;

import com.gxgz.common.core.utils.StringUtils;
import lombok.Getter;

@Getter
public enum OnChainClassifyEnum {
    /**
     * 数据存证
     */
    DATA("数据存证", "1"),
    /**
     * 文件存证
     */
    FILE("文件存证", "2"),
    EMPTY_99(StringUtils.EMPTY, StringUtils.EMPTY);
    private String label;
    private String value;
    private OnChainClassifyEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public Boolean equals(String value) {
        return this.value.equals(value);
    }

    public static OnChainClassifyEnum getByValue(String value) {
        for (OnChainClassifyEnum status : OnChainClassifyEnum.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return EMPTY_99;
    }

    public static String getLabel(String value){
        for(OnChainClassifyEnum e : OnChainClassifyEnum.values()){
            if(e.value.equals(value)){
                return e.label;
            }
        }
        return StringUtils.EMPTY;
    }

    public boolean isEmpty(){
        return this.label.equals(StringUtils.EMPTY);
    }
}
