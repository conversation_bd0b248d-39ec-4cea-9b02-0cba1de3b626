package com.gxgz.common.pageoffice.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文档编辑视图对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentEditVO {

    /**
     * 文档ID
     */
    private String documentId;

    /**
     * PageOffice控件HTML代码
     */
    private String htmlCode;

    /**
     * 控件ID
     */
    private String controlId;

    /**
     * 编辑URL
     */
    private String editUrl;

    /**
     * 保存URL
     */
    private String saveUrl;

    /**
     * JavaScript代码
     */
    private String jsCode;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;
    /**
     * 文档保存地址
     */
    private String savePath;

    /**
     * 业务CODE 对应 生成策略的 StrategyType 值
     */
    private String ywTyp;
    /**
     * 附件类别 对应 ZmclxxTypeEnum
     */
    private String fjlb;

    /**
     * 生成 保存地址
     */
    private OssVo generateOss;
}
