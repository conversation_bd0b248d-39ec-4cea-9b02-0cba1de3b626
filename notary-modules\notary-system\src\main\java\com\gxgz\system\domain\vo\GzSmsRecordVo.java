package com.gxgz.system.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gxgz.system.domain.GzSmsRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gxgz.common.excel.annotation.ExcelDictFormat;
import com.gxgz.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 短信发送日志视图对象 gz_sms_record
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GzSmsRecord.class)
public class GzSmsRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 公证卷宗ID
     */
    @ExcelProperty(value = "公证卷宗ID")
    private Long gzjzId;

    /**
     * 当事人ID
     */
    @ExcelProperty(value = "当事人ID")
    private String dsrId;

    /**
     * 当事人姓名
     */
    @ExcelProperty(value = "当事人姓名")
    private String dsrName;

    /**
     * 当事人联系电话
     */
    @ExcelProperty(value = "当事人联系电话")
    private String dsrPhone;

    /**
     * 短信内容
     */
    @ExcelProperty(value = "短信内容")
    private String smsContent;

    /**
     * 发送时间(精确到秒)
     */
    @ExcelProperty(value = "发送时间(精确到秒)")
    private Date sendTime;

    /**
     * 发送状态
     */
    @ExcelProperty(value = "发送状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long sendStatus;

    /**
     * 发送反馈时间(精确到秒)
     */
    @ExcelProperty(value = "发送反馈时间(精确到秒)")
    private Date feedbackTime;

    /**
     * 发送反馈结果(失败原因等)
     */
    @ExcelProperty(value = "发送反馈结果(失败原因等)")
    private String feedbackResult;

    /**
     * 备注信息
     */
    @ExcelProperty(value = "备注信息")
    private String remark;

    private String gzjzBh;


}
