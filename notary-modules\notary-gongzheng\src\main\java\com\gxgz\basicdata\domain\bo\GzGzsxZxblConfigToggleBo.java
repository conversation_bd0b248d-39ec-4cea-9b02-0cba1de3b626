package com.gxgz.basicdata.domain.bo;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

/**
 * 在线办理配置状态切换业务对象
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
public class GzGzsxZxblConfigToggleBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 是否在线办理（1是/0否）
     */
    private Long isOnlineHandle;

    /**
     * 是否启用办理（1是/0否）
     */
    private Long isEnableHandle;
}

