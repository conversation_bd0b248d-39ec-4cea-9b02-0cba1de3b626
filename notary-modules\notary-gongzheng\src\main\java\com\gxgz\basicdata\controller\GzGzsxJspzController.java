package com.gxgz.basicdata.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.basicdata.domain.vo.GzGzsxJspzVo;
import com.gxgz.basicdata.domain.bo.GzGzsxJspzBo;
import com.gxgz.basicdata.service.IGzGzsxJspzService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 角色配置
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/gzsxJspz")
public class GzGzsxJspzController extends BaseController {

    private final IGzGzsxJspzService gzGzsxJspzService;

    /**
     * 查询角色配置列表
     */
//    @SaCheckPermission("basicdata:gzsxJspz:list")
    @GetMapping("/list")
    public TableDataInfo<GzGzsxJspzVo> list(GzGzsxJspzBo bo, PageQuery pageQuery) {
        return gzGzsxJspzService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出角色配置列表
     */
//    @SaCheckPermission("basicdata:gzsxJspz:export")
    @Log(title = "角色配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGzsxJspzBo bo, HttpServletResponse response) {
        List<GzGzsxJspzVo> list = gzGzsxJspzService.queryList(bo);
        ExcelUtil.exportExcel(list, "角色配置", GzGzsxJspzVo.class, response);
    }

    /**
     * 获取角色配置详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:gzsxJspz:query")
    @GetMapping("/{id}")
    public R<GzGzsxJspzVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGzsxJspzService.queryById(id));
    }

    /**
     * 新增角色配置
     */
//    @SaCheckPermission("basicdata:gzsxJspz:add")
    @Log(title = "角色配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGzsxJspzBo bo) {
        return toAjax(gzGzsxJspzService.insertByBo(bo));
    }

    /**
     * 修改角色配置
     */
//    @SaCheckPermission("basicdata:gzsxJspz:edit")
    @Log(title = "角色配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGzsxJspzBo bo) {
        return toAjax(gzGzsxJspzService.updateByBo(bo));
    }

    /**
     * 删除角色配置
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:gzsxJspz:remove")
    @Log(title = "角色配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGzsxJspzService.deleteWithValidByIds(List.of(ids), true));
    }
}
