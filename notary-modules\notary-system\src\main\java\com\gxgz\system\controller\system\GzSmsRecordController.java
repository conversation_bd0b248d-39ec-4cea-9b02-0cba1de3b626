package com.gxgz.system.controller.system;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.system.domain.vo.GzSmsRecordVo;
import com.gxgz.system.domain.bo.GzSmsRecordBo;
import com.gxgz.system.service.IGzSmsRecordService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 短信发送日志
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/smsRecord")
public class GzSmsRecordController extends BaseController {

    private final IGzSmsRecordService gzSmsRecordService;

    /**
     * 查询短信发送日志列表
     */
    @SaCheckPermission("system:smsRecord:list")
    @GetMapping("/list")
    public TableDataInfo<GzSmsRecordVo> list(GzSmsRecordBo bo, PageQuery pageQuery) {
        return gzSmsRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出短信发送日志列表
     */
    @SaCheckPermission("system:smsRecord:export")
    @Log(title = "短信发送日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzSmsRecordBo bo, HttpServletResponse response) {
        List<GzSmsRecordVo> list = gzSmsRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "短信发送日志", GzSmsRecordVo.class, response);
    }

    /**
     * 获取短信发送日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:smsRecord:query")
    @GetMapping("/{id}")
    public R<GzSmsRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzSmsRecordService.queryById(id));
    }

    /**
     * 新增短信发送日志
     */
    @SaCheckPermission("system:smsRecord:add")
    @Log(title = "短信发送日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzSmsRecordBo bo) {
        return toAjax(gzSmsRecordService.insertByBo(bo));
    }

    /**
     * 修改短信发送日志
     */
    @SaCheckPermission("system:smsRecord:edit")
    @Log(title = "短信发送日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzSmsRecordBo bo) {
        return toAjax(gzSmsRecordService.updateByBo(bo));
    }

    /**
     * 删除短信发送日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:smsRecord:remove")
    @Log(title = "短信发送日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzSmsRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
