package com.gxgz.basicdata.domain;

import com.gxgz.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 在线办理材料管理对象 gz_gzsx_zxbl_material
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gz_gzsx_zxbl_material")
public class GzGzsxZxblMaterial extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 材料名称
     */
    private String materialName;

    /**
     * 类别：1-个人，2-企业
     */
    private Long category;

    /**
     * 说明
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识：0-正常，1-删除
     */
    @TableLogic
    private String delFlag;


}
