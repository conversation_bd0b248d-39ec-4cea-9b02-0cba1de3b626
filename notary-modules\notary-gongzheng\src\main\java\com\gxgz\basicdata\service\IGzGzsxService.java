package com.gxgz.basicdata.service;

import com.gxgz.basicdata.domain.bo.GzGzsxZxblBo;
import com.gxgz.basicdata.domain.vo.GzGzsxVo;
import com.gxgz.basicdata.domain.bo.GzGzsxBo;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 基础数据-公证事项Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IGzGzsxService {

    /**
     * 查询基础数据-公证事项
     *
     * @param id 主键
     * @return 基础数据-公证事项
     */
    GzGzsxVo queryById(Long id);

    /**
     * 分页查询基础数据-公证事项列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 基础数据-公证事项分页列表
     */
    TableDataInfo<GzGzsxVo> queryPageList(GzGzsxBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的基础数据-公证事项列表
     *
     * @param bo 查询条件
     * @return 基础数据-公证事项列表
     */
    List<GzGzsxVo> queryList(GzGzsxBo bo);

    /**
     * 新增基础数据-公证事项
     *
     * @param bo 基础数据-公证事项
     * @return 是否新增成功
     */
    Boolean insertByBo(GzGzsxBo bo);

    /**
     * 修改基础数据-公证事项
     *
     * @param bo 基础数据-公证事项
     * @return 是否修改成功
     */
    Boolean updateByBo(GzGzsxBo bo);

    /**
     * 校验并批量删除基础数据-公证事项信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 所有数据
     * @param bo
     * @return
     */
    List<GzGzsxVo> listTree(GzGzsxBo bo);

    List<GzGzsxVo> listExclude(Long id);

    List<GzGzsxVo> listTreeByZxbl(GzGzsxZxblBo bo);
}
