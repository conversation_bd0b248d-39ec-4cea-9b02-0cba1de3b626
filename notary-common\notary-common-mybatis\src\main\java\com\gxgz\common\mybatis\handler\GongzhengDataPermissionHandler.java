package com.gxgz.common.mybatis.handler;

import cn.hutool.core.util.StrUtil;
import com.gxgz.common.core.domain.model.LoginUser;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.mybatis.helper.DataPermissionHelper;
import com.gxgz.common.satoken.utils.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 公证业务数据权限处理器
 * 专门处理 gz_ 开头业务表的数据权限控制
 * 
 * 注意：本处理器只负责业务层面的权限控制（create_dept 和 create_by），
 * 租户隔离（tenant_id）由现有的数据权限框架自动处理，不会重复添加。
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class GongzhengDataPermissionHandler {

    /**
     * 公证业务表前缀
     */
    private static final String GONGZHENG_TABLE_PREFIX = "gz_";

    /**
     * 是否启用公证业务数据权限控制
     */
    @Value("${gongzheng.data-permission.enabled:true}")
    private boolean enabled;

    /**
     * 公证业务表前缀
     */
    @Value("${gongzheng.data-permission.table-prefix:gz_}")
    private String tablePrefix;

    /**
     * 需要权限控制的表名列表（逗号分隔）
     */
    @Value("${gongzheng.data-permission.controlled-tables:}")
    private String controlledTables;

    /**
     * 忽略权限控制的角色
     */
    @Value("${gongzheng.data-permission.ignore-roles:superadmin,sysadmin}")
    private String ignoreRoles;

    /**
     * 日志级别
     */
    @Value("${gongzheng.data-permission.log-level:INFO}")
    private String logLevel;

    /**
     * 需要权限控制的表名集合
     */
    private Set<String> controlledTablesSet;

    /**
     * 忽略权限控制的角色集合
     */
    private Set<String> ignoreRolesSet;

    /**
     * 初始化配置
     */
    private void initConfig() {
        if (controlledTablesSet == null) {
            controlledTablesSet = new HashSet<>();
            if (StringUtils.isNotBlank(controlledTables)) {
                String[] tables = controlledTables.split(",");
                for (String table : tables) {
                    String cleanTable = table.trim();
                    if (StringUtils.isNotBlank(cleanTable)) {
                        controlledTablesSet.add(cleanTable.toLowerCase());
                    }
                }
            }
            log.info("初始化公证业务权限控制表: {}", controlledTablesSet);
        }

        if (ignoreRolesSet == null) {
            ignoreRolesSet = new HashSet<>();
            if (StringUtils.isNotBlank(ignoreRoles)) {
                String[] roles = ignoreRoles.split(",");
                for (String role : roles) {
                    String cleanRole = role.trim();
                    if (StringUtils.isNotBlank(cleanRole)) {
                        ignoreRolesSet.add(cleanRole);
                    }
                }
            }
            log.info("初始化忽略权限控制的角色: {}", ignoreRolesSet);
        }
    }

    /**
     * 构建公证业务数据权限过滤条件
     *
     * @param tableName 表名
     * @param user      当前登录用户
     * @return SQL过滤条件，如果不需要过滤则返回null
     */
    public String buildGongzhengPermissionCondition(String tableName, LoginUser user) {
        // 检查功能是否启用
        if (!enabled) {
            log.debug("公证业务数据权限控制功能已禁用");
            return null;
        }

        // 检查是否为公证业务表且需要权限控制
        if (!isTableNeedPermissionControl(tableName)) {
            log.debug("表 {} 不需要权限控制，跳过权限过滤", tableName);
            return null;
        }

        // 超级管理员和系统管理员跳过权限过滤
        if (isAdminUser(user)) {
            log.debug("用户 {} 为管理员，跳过公证业务权限过滤", user.getUsername());
            return null;
        }

        // 获取用户的查询部门权限 - 需要从SysUser表获取queryDbIds字段 逗号分隔
        String queryDbIds = user.getQueryDbIds();

        if (StringUtils.isNotBlank(queryDbIds)) {
            // 有部门权限，根据 create_dept 过滤
            // 处理逗号分隔的部门ID，转换为 IN 查询条件
            // 清理和验证部门ID格式
            String cleanQueryDbIds = cleanAndValidateDeptIds(queryDbIds);
            if (StringUtils.isNotBlank(cleanQueryDbIds)) {
                // 只添加 create_dept 条件，不添加租户条件（已有框架处理）
                String condition = String.format("%s.create_dept IN (%s)", tableName, cleanQueryDbIds);
                log.debug("用户 {} 有部门权限 {}，应用部门过滤条件: {}", user.getUsername(), cleanQueryDbIds, condition);
                return condition;
            } else {
                log.warn("用户 {} 的部门权限格式无效: {}", user.getUsername(), queryDbIds);
                // 如果部门权限格式无效，降级为只能查询自己创建的数据
                String condition = String.format("%s.create_by = %d", tableName, user.getUserId());
                log.debug("用户 {} 部门权限无效，降级为创建者过滤条件: {}", user.getUsername(), condition);
                return condition;
            }
        } else {
            // 无部门权限，只能查询自己创建的数据
            // 只添加 create_by 条件，不添加租户条件（已有框架处理）
            String condition = String.format("%s.create_by = %d", tableName, user.getUserId());
            log.debug("用户 {} 无部门权限，应用创建者过滤条件: {}", user.getUsername(), condition);
            return condition;
        }
    }

    /**
     * 检查表是否需要权限控制
     * 只有配置在 controlled-tables 中的表才需要权限控制
     *
     * @param tableName 表名
     * @return 是否需要权限控制
     */
    public boolean isTableNeedPermissionControl(String tableName) {
        // 检查是否为公证业务表
        if (!isGongzhengTable(tableName)) {
            return false;
        }

        // 初始化配置
        initConfig();

        // 检查是否在权限控制表列表中
        boolean needControl = controlledTablesSet.contains(tableName.toLowerCase());
        
        if (needControl) {
            log.debug("表 {} 需要权限控制", tableName);
        } else {
            log.debug("表 {} 不需要权限控制，未在配置列表中", tableName);
        }
        
        return needControl;
    }

    /**
     * 检查是否为公证业务表
     *
     * @param tableName 表名
     * @return 是否为公证业务表
     */
    public boolean isGongzhengTable(String tableName) {
        return StringUtils.isNotBlank(tableName) &&
            tableName.toLowerCase().startsWith(tablePrefix.toLowerCase());
    }

    /**
     * 检查用户是否为管理员
     *
     * @param user 用户信息
     * @return 是否为管理员
     */
    private boolean isAdminUser(LoginUser user) {
        if (user == null || user.getRolePermission() == null) {
            return false;
        }

        // 初始化配置
        initConfig();

        // 检查用户角色是否在忽略列表中
        for (String role : user.getRolePermission()) {
            if (ignoreRolesSet.contains(role)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取当前登录用户
     *
     * @return 当前登录用户
     */
    private LoginUser getCurrentUser() {
        LoginUser user = DataPermissionHelper.getVariable("user");
        if (user == null) {
            user = LoginHelper.getLoginUser();
            if (user != null) {
                DataPermissionHelper.setVariable("user", user);
            }
        }
        return user;
    }

    /**
     * 构建完整的权限过滤条件
     *
     * @param tableName 表名
     * @return SQL过滤条件
     */
    public String buildPermissionCondition(String tableName) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            log.warn("无法获取当前登录用户信息");
            return null;
        }

        return buildGongzhengPermissionCondition(tableName, user);
    }

    /**
     * 清理和验证部门ID字符串
     * 处理逗号分隔的部门ID，移除无效字符，确保格式正确
     *
     * @param queryDbIds 原始部门ID字符串，如 "100,101,102" 或 "100, 101, 102"
     * @return 清理后的部门ID字符串，如 "100,101,102"
     */
    private String cleanAndValidateDeptIds(String queryDbIds) {
        if (StringUtils.isBlank(queryDbIds)) {
            return null;
        }

        try {
            // 按逗号分割，清理每个ID
            String[] deptIds = queryDbIds.split(",");
            StringBuilder cleanIds = new StringBuilder();
            
            for (int i = 0; i < deptIds.length; i++) {
                String deptId = deptIds[i].trim();
                if (StringUtils.isNotBlank(deptId)) {
                    // 验证是否为有效的数字
                    try {
                        Long.parseLong(deptId);
                        if (cleanIds.length() > 0) {
                            cleanIds.append(",");
                        }
                        cleanIds.append(deptId);
                    } catch (NumberFormatException e) {
                        log.warn("无效的部门ID格式: {}", deptId);
                        // 跳过无效的ID
                    }
                }
            }
            
            String result = cleanIds.toString();
            if (StringUtils.isBlank(result)) {
                log.warn("清理后的部门ID为空，原始值: {}", queryDbIds);
                return null;
            }
            
            return result;
        } catch (Exception e) {
            log.error("清理部门ID时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取当前配置信息（用于调试）
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        initConfig();
        return String.format("enabled=%s, tablePrefix=%s, controlledTables=%s, ignoreRoles=%s, logLevel=%s",
            enabled, tablePrefix, controlledTablesSet, ignoreRolesSet, logLevel);
    }
}
