package com.gxgz.log.domain;

import com.gxgz.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 日志-人脸对比记录对象 gz_log_rldbjl
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gz_log_rldbjl")
public class GzLogRldbjl extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 对比人脸图片
     */
    private String dbrl;

    /**
     * 对比结果
     */
    private String dbjg;
    /**
     * 当事人ID
     */
    private Long dsrId;
    /**
     * 当事人姓名
     */
    private String dsrXm;
    /**
     * 对比指数
     */
    private String dbzs;
    /**
     * 对比日期
     */
    private Date dbrq;
}
