package com.gxgz.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 同步 模板文档类型 字典 gz_mb_wdlb
 * 模板类型枚举，与数据库字典表保持同步
 */
@Getter
@AllArgsConstructor
public enum TemplateTypeEnum {
    // 基础文档类型
    WS("文书", 1,"DOCUMENT"),
    BL("笔录", 2,"RECORD"),
    GZSYW("公证书译文", 5,"NOTARIZATION_TRANSLATION"),
    GRXX("个人信息", 6,"DSR_INFO"),
    OTHER("其他证明书", 85,""), //无模板
    RSDAB("人事档案/个人信息", 80,"DSR_INFO"),
    GZS("公证书", 3,"NOTARIZATION"),

    SQB("申请表", 8,"NOTARIAL_APPLICATION_FORM"),
    GRSQB("个人申请表", 114,"NOTARIAL_APPLICATION_FORM"),
    QYSQB("企业申请表", 115,"NOTARIAL_APPLICATION_FORM"),
    // 审批表类型
    SLTZD("受理通知单", 9,"ACCEPTANCE_NOTICE"),
    GZSPB("审批表", 11,"GONGZHENG_APPROVA"), //国内
    GZSWSPB("公证涉外审批表", 12,"GONGZHENG_SW_APPROVA"),
    SDHZ("送达回执", 13,"DELIVERY_RECEIPT"),
    QFG("签发稿", 20,"ISSUING_DRAFT"),

    // 查询和调取类型
    CKXH("存款查询涵", 79,"DEPOSIT_INQUIRY"),
    JSX("介绍信（内部）", 86,"LETTER_OF_INTRODUCTION"),
    JSXWB("介绍信（外部）", 87,""),
    // 费用相关
    FYBTSPB("费用补退审批表", 92,"EXPENSE_REIMBURSEMENT_APPROVAL"),
    BZB("补正模板", 93,"CORRECTION_TEMPLATE"),
    SJD("收据单", 78,""), // 旧系统没有模板 该项未实现
    // 比对类型
    MBRDB("面部识别对比", 94,"FACIAL_RECOGNITION_COMPARISON"),
    ZWDB("指纹对比", 95,"FINGERPRINT_COMPARISON"),
    XCJL("现场记录", 23,"XCJL"),
    HSJLD("核实记录单", 96,""),// 旧系统没有模板 该项未实现
    // 系统管理类型
    TFD("退费单", 97,"REFUND_BILL"), // 旧系统没有模板 该项未实现
    ZXD("公证咨询单", 99,"ZXD"),
    ZZGZQKSM("终止公证情况说明", 100,"TERMINATE_NOTARIZATION"),// 旧系统没有模板 该项未实现
    BYBLGZQKSM("不予办理公证情况说明", 101,"REFUSE_NOTARIZATION"),// 旧系统没有模板 该项未实现
    SHMB("书函模板", 102,""),// 旧系统没有模板 该项未实现
    DZGZFM("电子公证书封面", 109,""),// 旧系统没有模板 该项未实现
    // 卷宗和档案类型
    JZFP("卷宗封皮", 10,"JZFP"),
    DDHZ("调档回执", 89,""),
    CPB("呈批表", 81,""),
    BPB("报批表", 82,""),


    // 申请表格类型
    JCRQSQB("继承人申请表格", 110,"INHERITANCE_RIGHT_APPLICATION"),
    FQJCRQSQB("放弃继承人申请表格", 111,"RENUNCIATION_OF_SUCCESSION"),
    WGSYSQB("外国收养申请表格", 112,"FOREIGN_ADOPTION_APPLICATION"),
    CJYBHZB("采集样本回执表格", 113,"COLLECT_SAMPLE_RECEIPT"),
    NOTICE("告知书",116,"NOTICE"),
    // 收费类型
    SFD("缴费清单", 77,"BILL"),


    BDCCX("不动产查询函", 103,"");

    /**
     * 模板类型名称
     */
    private final String name;

    /**
     * 模板类型代码
     */
    private final Integer code;
    private final String codeStr;
    /**
     * 根据代码获取枚举
     *
     * @param code 模板类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TemplateTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (TemplateTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 模板类型名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TemplateTypeEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }

        for (TemplateTypeEnum type : values()) {
            if (type.getName().equals(name.trim())) {
                return type;
            }
        }
        return null;
    }
}
