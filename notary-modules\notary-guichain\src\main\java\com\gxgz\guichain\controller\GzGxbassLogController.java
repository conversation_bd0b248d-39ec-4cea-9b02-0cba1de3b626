package com.gxgz.guichain.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.guichain.domain.vo.GzGxbassLogVo;
import com.gxgz.guichain.domain.bo.GzGxbassLogBo;
import com.gxgz.guichain.service.IGzGxbassLogService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 公证-桂链-上链日志
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/guichain/gxbassLog")
public class GzGxbassLogController extends BaseController {

    private final IGzGxbassLogService gzGxbassLogService;

    /**
     * 查询公证-桂链-上链日志列表
     */
    @SaCheckPermission("guichain:gxbassLog:list")
    @GetMapping("/list")
    public TableDataInfo<GzGxbassLogVo> list(GzGxbassLogBo bo, PageQuery pageQuery) {
        return gzGxbassLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出公证-桂链-上链日志列表
     */
    @SaCheckPermission("guichain:gxbassLog:export")
    @Log(title = "公证-桂链-上链日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzGxbassLogBo bo, HttpServletResponse response) {
        List<GzGxbassLogVo> list = gzGxbassLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "公证-桂链-上链日志", GzGxbassLogVo.class, response);
    }

    /**
     * 获取公证-桂链-上链日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("guichain:gxbassLog:query")
    @GetMapping("/{id}")
    public R<GzGxbassLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzGxbassLogService.queryById(id));
    }

    /**
     * 新增公证-桂链-上链日志
     */
    @SaCheckPermission("guichain:gxbassLog:add")
    @Log(title = "公证-桂链-上链日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzGxbassLogBo bo) {
        return toAjax(gzGxbassLogService.insertByBo(bo));
    }

    /**
     * 修改公证-桂链-上链日志
     */
    @SaCheckPermission("guichain:gxbassLog:edit")
    @Log(title = "公证-桂链-上链日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzGxbassLogBo bo) {
        return toAjax(gzGxbassLogService.updateByBo(bo));
    }

    /**
     * 删除公证-桂链-上链日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("guichain:gxbassLog:remove")
    @Log(title = "公证-桂链-上链日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzGxbassLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
