package com.gxgz.dsr.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.dsr.domain.vo.GzDsrxxZwxxVo;
import com.gxgz.dsr.domain.bo.GzDsrxxZwxxBo;
import com.gxgz.dsr.service.IGzDsrxxZwxxService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 当事人-指纹信息
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dsr/dsrxxZwxx")
public class GzDsrxxZwxxController extends BaseController {

    private final IGzDsrxxZwxxService gzDsrxxZwxxService;

    /**
     * 查询当事人-指纹信息列表
     */
//    @SaCheckPermission("dsr:dsrxxZwxx:list")
    @GetMapping("/list")
    public TableDataInfo<GzDsrxxZwxxVo> list(GzDsrxxZwxxBo bo, PageQuery pageQuery) {
        return gzDsrxxZwxxService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出当事人-指纹信息列表
     */
//    @SaCheckPermission("dsr:dsrxxZwxx:export")
    @Log(title = "当事人-指纹信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzDsrxxZwxxBo bo, HttpServletResponse response) {
        List<GzDsrxxZwxxVo> list = gzDsrxxZwxxService.queryList(bo);
        ExcelUtil.exportExcel(list, "当事人-指纹信息", GzDsrxxZwxxVo.class, response);
    }

    /**
     * 获取当事人-指纹信息详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("dsr:dsrxxZwxx:query")
    @GetMapping("/{id}")
    public R<GzDsrxxZwxxVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzDsrxxZwxxService.queryById(id));
    }

    /**
     * 新增当事人-指纹信息
     */
//    @SaCheckPermission("dsr:dsrxxZwxx:add")
    @Log(title = "当事人-指纹信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzDsrxxZwxxBo bo) {
        return toAjax(gzDsrxxZwxxService.insertByBo(bo));
    }

    /**
     * 修改当事人-指纹信息
     */
//    @SaCheckPermission("dsr:dsrxxZwxx:edit")
    @Log(title = "当事人-指纹信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzDsrxxZwxxBo bo) {
        return toAjax(gzDsrxxZwxxService.updateByBo(bo));
    }

    /**
     * 删除当事人-指纹信息
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("dsr:dsrxxZwxx:remove")
    @Log(title = "当事人-指纹信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzDsrxxZwxxService.deleteWithValidByIds(List.of(ids), true));
    }
}
