package com.gxgz.common.mybatis.config;

import com.gxgz.common.mybatis.handler.GongzhengDataPermissionHandler;
import com.gxgz.common.mybatis.interceptor.GongzhengDataPermissionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 公证业务数据权限自动配置类
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "gongzheng.data-permission.enabled", havingValue = "true", matchIfMissing = true)
public class GongzhengDataPermissionAutoConfiguration {

    /**
     * 配置公证业务权限处理器
     */
    @Bean
    public GongzhengDataPermissionHandler gongzhengDataPermissionHandler() {
        log.info("初始化公证业务数据权限处理器");
        return new GongzhengDataPermissionHandler();
    }

    /**
     * 配置公证业务权限拦截器
     */
    @Bean
    public GongzhengDataPermissionInterceptor gongzhengDataPermissionInterceptor(GongzhengDataPermissionHandler handler) {
        log.info("初始化公证业务数据权限拦截器");
        return new GongzhengDataPermissionInterceptor(handler);
    }
}





