package com.gxgz.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.mybatis.core.page.TableDataInfo;
import com.gxgz.system.domain.bo.SysDictDataBo;
import com.gxgz.system.domain.vo.SysDictDataVo;
import com.gxgz.system.service.ISysDictDataService;
import com.gxgz.system.service.ISysDictTypeService;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.log.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据字典信息
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController {

    private final ISysDictDataService dictDataService;
    private final ISysDictTypeService dictTypeService;

    /**
     * 查询字典数据列表
     */
//    @SaCheckPermission("system:dict:list")
    @GetMapping("/list")
    public TableDataInfo<SysDictDataVo> list(SysDictDataBo dictData, PageQuery pageQuery) {
        return dictDataService.selectPageDictDataList(dictData, pageQuery);
    }

    /**
     * 导出字典数据列表
     */
    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
//    @SaCheckPermission("system:dict:export")
    @PostMapping("/export")
    public void export(SysDictDataBo dictData, HttpServletResponse response) {
        List<SysDictDataVo> list = dictDataService.selectDictDataList(dictData);
        ExcelUtil.exportExcel(list, "字典数据", SysDictDataVo.class, response);
    }

    /**
     * 查询字典数据详细
     *
     * @param dictCode 字典code
     */
//    @SaCheckPermission("system:dict:query")
    @GetMapping(value = "/{dictCode}")
    public R<SysDictDataVo> getInfo(@PathVariable Long dictCode) {
        return R.ok(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     *
     * @param dictType 字典类型
     */
    @GetMapping(value = "/type/{dictType}")
    public R<List<SysDictDataVo>> dictType(@PathVariable String dictType) {
        List<SysDictDataVo> data = dictTypeService.selectDictDataByType(dictType);
        if (ObjectUtil.isNull(data)) {
            data = new ArrayList<>();
        }
        return R.ok(data);
    }

    /**
     * 新增字典类型
     */
//    @SaCheckPermission("system:dict:add")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysDictDataBo dict) {
        if (!dictDataService.checkDictDataUnique(dict)) {
            return R.fail("新增字典数据'" + dict.getDictValue() + "'失败，字典键值已存在");
        }
        dictDataService.insertDictData(dict);
        return R.ok();
    }

    /**
     * 修改保存字典类型
     */
//    @SaCheckPermission("system:dict:edit")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysDictDataBo dict) {
        if (!dictDataService.checkDictDataUnique(dict)) {
            return R.fail("修改字典数据'" + dict.getDictValue() + "'失败，字典键值已存在");
        }
        dictDataService.updateDictData(dict);
        return R.ok();
    }

    /**
     * 删除字典类型
     *
     * @param dictCodes 字典code串
     */
//    @SaCheckPermission("system:dict:remove")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public R<Void> remove(@PathVariable Long[] dictCodes) {
        dictDataService.deleteDictDataByIds(dictCodes);
        return R.ok();
    }

    /**
     * 新增字典类型
     */
//    @SaCheckPermission("system:dict:add")
    @PostMapping("/importJSON")
    public R<Void> importJSON(SysDictDataBo dict, @RequestPart("file") MultipartFile file) {
        // 检查是否为JOSN文件
        if(file == null || Objects.requireNonNull(file.getOriginalFilename()).toLowerCase().lastIndexOf(".json") == -1){
            throw new RuntimeException("请上传JSON文件");
        }
        String jsonStr = "";
        try {
            byte[] data = file.getBytes();
            jsonStr = new String(data, StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONArray jsonArray = JSONUtil.parseArray(jsonStr);
        int total = jsonArray.size();
        SysDictDataBo add;
        for (int i = 0; i < total; i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            add = new SysDictDataBo();
            if(StringUtils.isNotBlank(dict.getDictLabel())){
                add.setDictLabel(jsonObject.getStr(dict.getDictLabel()));
            }
            if(StringUtils.isNotBlank(dict.getDictValue())){
                add.setDictValue(jsonObject.getStr(dict.getDictValue()));
            }
            if(StringUtils.isNotBlank(dict.getDictType())){
                add.setDictType(dict.getDictType());
            }
            if(StringUtils.isNotBlank(dict.getDictValue())){
                add.setDictValue(jsonObject.getStr(dict.getDictValue()));
            }
            if(StringUtils.isNotBlank(dict.getDictSortCode())){
                add.setDictSort(jsonObject.getInt(dict.getDictSortCode(), 0));
            }else {
                add.setDictSort(i);
            }
            add.setListClass("primary");
            add.setIsDefault("N");
            dictDataService.insertDictData(add);
        }

        return R.ok();
    }

}
