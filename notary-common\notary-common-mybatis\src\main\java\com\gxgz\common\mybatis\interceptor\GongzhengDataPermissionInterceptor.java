package com.gxgz.common.mybatis.interceptor;

import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.BaseMultiTableInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.gxgz.common.core.utils.StringUtils;
import com.gxgz.common.mybatis.handler.GongzhengDataPermissionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 公证业务数据权限拦截器
 * 专门拦截 gz_ 开头业务表的查询，自动添加权限过滤条件
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(100) // 在现有权限拦截器之前执行
public class GongzhengDataPermissionInterceptor extends BaseMultiTableInnerInterceptor implements InnerInterceptor {

    private final GongzhengDataPermissionHandler gongzhengPermissionHandler;

    /**
     * 在执行查询之前，检查并处理公证业务数据权限相关逻辑
     */
    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        // 检查是否需要忽略数据权限处理
        if (InterceptorIgnoreHelper.willIgnoreDataPermission(ms.getId())) {
            return;
        }

        // 解析 sql 分配对应方法
        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        String processedSql = processGongzhengPermission(mpBs.sql(), ms.getId());
        mpBs.sql(processedSql);
    }

    /**
     * 处理公证业务数据权限
     */
    private String processGongzhengPermission(String sql, String msId) {
        try {
            if (StringUtils.isBlank(sql)) {
                return sql;
            }

            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                return processSelectStatement((Select) statement);
            } else if (statement instanceof Update) {
                return processUpdateStatement((Update) statement);
            } else if (statement instanceof Delete) {
                return processDeleteStatement((Delete) statement);
            }

            return sql;
        } catch (JSQLParserException e) {
            log.warn("解析SQL失败，跳过公证业务权限处理: {}", e.getMessage());
            return sql;
        } catch (Exception e) {
            log.error("处理公证业务权限时发生异常: {}", e.getMessage(), e);
            return sql;
        }
    }

    /**
     * 处理SELECT语句
     */
    private String processSelectStatement(Select selectStatement) {
        try {
            if (selectStatement.getSelectBody() instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectStatement.getSelectBody();
                addPermissionCondition(plainSelect);
            }
            return selectStatement.toString();
        } catch (Exception e) {
            log.warn("处理SELECT语句失败: {}", e.getMessage());
            return selectStatement.toString();
        }
    }

    /**
     * 处理UPDATE语句
     */
    private String processUpdateStatement(Update updateStatement) {
        try {
            Table table = updateStatement.getTable();
            if (table != null && gongzhengPermissionHandler.isGongzhengTable(table.getName())) {
                String condition = gongzhengPermissionHandler.buildPermissionCondition(table.getName());
                if (StringUtils.isNotBlank(condition)) {
                    Expression where = updateStatement.getWhere();
                    if (where == null) {
                        updateStatement.setWhere(CCJSqlParserUtil.parseExpression(condition));
                    } else {
                        AndExpression andExpr = new AndExpression(where, CCJSqlParserUtil.parseExpression(condition));
                        updateStatement.setWhere(andExpr);
                    }
                }
            }
            return updateStatement.toString();
        } catch (Exception e) {
            log.warn("处理UPDATE语句失败: {}", e.getMessage());
            return updateStatement.toString();
        }
    }

    /**
     * 处理DELETE语句
     */
    private String processDeleteStatement(Delete deleteStatement) {
        try {
            Table table = deleteStatement.getTable();
            if (table != null && gongzhengPermissionHandler.isGongzhengTable(table.getName())) {
                String condition = gongzhengPermissionHandler.buildPermissionCondition(table.getName());
                if (StringUtils.isNotBlank(condition)) {
                    Expression where = deleteStatement.getWhere();
                    if (where == null) {
                        deleteStatement.setWhere(CCJSqlParserUtil.parseExpression(condition));
                    } else {
                        AndExpression andExpr = new AndExpression(where, CCJSqlParserUtil.parseExpression(condition));
                        deleteStatement.setWhere(andExpr);
                    }
                }
            }
            return deleteStatement.toString();
        } catch (Exception e) {
            log.warn("处理DELETE语句失败: {}", e.getMessage());
            return deleteStatement.toString();
        }
    }

    /**
     * 为SELECT语句添加权限条件
     */
    private void addPermissionCondition(PlainSelect plainSelect) {
        try {
            List<Table> tables = new ArrayList<>();
            
            // 获取主表
            FromItem fromItem = plainSelect.getFromItem();
            if (fromItem instanceof Table) {
                Table table = (Table) fromItem;
                tables.add(table);
            }

            // 获取关联表
            List<Join> joins = plainSelect.getJoins();
            if (joins != null) {
                for (Join join : joins) {
                    FromItem rightItem = join.getRightItem();
                    if (rightItem instanceof Table) {
                        Table table = (Table) rightItem;
                        tables.add(table);
                    }
                }
            }

            // 为每个公证业务表添加权限条件
            for (Table table : tables) {
                if (gongzhengPermissionHandler.isGongzhengTable(table.getName())) {
                    String condition = gongzhengPermissionHandler.buildPermissionCondition(table.getName());
                    if (StringUtils.isNotBlank(condition)) {
                        addWhereCondition(plainSelect, condition);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("添加权限条件失败: {}", e.getMessage());
        }
    }

    /**
     * 添加WHERE条件
     */
    private void addWhereCondition(PlainSelect plainSelect, String condition) {
        try {
            Expression permissionExpr = CCJSqlParserUtil.parseExpression(condition);
            Expression where = plainSelect.getWhere();
            
            if (where == null) {
                plainSelect.setWhere(permissionExpr);
            } else {
                AndExpression andExpr = new AndExpression(where, permissionExpr);
                plainSelect.setWhere(andExpr);
            }
            
            log.debug("为表添加权限条件: {}", condition);
        } catch (JSQLParserException e) {
            log.warn("解析权限条件失败: {}", e.getMessage());
        }
    }

    /**
     * 实现抽象方法 buildTableExpression
     */
    @Override
    public Expression buildTableExpression(Table table, Expression where, String mappedStatementId) {
        // 检查是否为公证业务表
        if (gongzhengPermissionHandler.isGongzhengTable(table.getName())) {
            String condition = gongzhengPermissionHandler.buildPermissionCondition(table.getName());
            if (StringUtils.isNotBlank(condition)) {
                try {
                    Expression permissionExpr = CCJSqlParserUtil.parseExpression(condition);
                    if (where == null) {
                        return permissionExpr;
                    } else {
                        return new AndExpression(where, permissionExpr);
                    }
                } catch (JSQLParserException e) {
                    log.warn("解析权限条件失败: {}", e.getMessage());
                }
            }
        }
        return where;
    }
}
