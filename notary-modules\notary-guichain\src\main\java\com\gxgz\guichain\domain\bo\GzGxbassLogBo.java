package com.gxgz.guichain.domain.bo;

import com.gxgz.guichain.domain.GzGxbassLog;
import com.gxgz.common.mybatis.core.domain.BaseEntity;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 公证-桂链-上链日志业务对象 gz_gxbass_log
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GzGxbassLog.class, reverseConvertGenerate = false)
public class GzGxbassLogBo extends BaseEntity {

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 上链描述
     */
    private String chainDec;

    /**
     * 上链数据
     */
    private String chainData;

    /**
     * 上链文件
     */
    private Long ossId;

    /**
     * 存证类型 1文件存证 2 数据存证
     */
    private Long chainType;

    /**
     * 虚拟通道ID，非虚拟通道加密方式返回空
     */
    private String channelId;

    /**
     * 加密方式
     */
    private Long encryptMode;

    /**
     * 交易哈希
     */
    private String txHash;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 业务分类
     */
    private String bizClassify;


}
