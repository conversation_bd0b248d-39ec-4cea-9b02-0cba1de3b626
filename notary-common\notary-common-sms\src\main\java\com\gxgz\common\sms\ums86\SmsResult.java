package com.gxgz.common.sms.ums86;

public class SmsResult {
    private final int resultCode;       // 结果代码 0表示成功
    private final String description;   // 结果描述
    private final String taskId;        // 任务ID
    private final String failList;      // 发送失败的号码列表
    private final String taskId2;       // 任务ID(备用字段)

    public SmsResult(int resultCode, String description, String taskId, String failList, String taskId2) {
        this.resultCode = resultCode;
        this.description = description;
        this.taskId = taskId;
        this.failList = failList;
        this.taskId2 = taskId2;
    }

    // Getter方法
    public int getResultCode() {
        return resultCode;
    }

    public String getDescription() {
        return description;
    }

    public String getTaskId() {
        return taskId;
    }

    public String getFailList() {
        return failList;
    }

    public String getTaskId2() {
        return taskId2;
    }

    /**
     * 判断是否发送成功
     * @return 发送成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return resultCode == 0;
    }

    @Override
    public String toString() {
        return "SmsResult{" +
            "resultCode=" + resultCode +
            ", description='" + description + '\'' +
            ", taskId='" + taskId + '\'' +
            ", failList='" + failList + '\'' +
            ", taskId2='" + taskId2 + '\'' +
            '}';
    }
}
