package com.gxgz.basicdata.controller;

import java.io.IOException;
import java.util.List;

import com.gxgz.common.excel.utils.ExcelUtil;
import com.gxgz.common.idempotent.annotation.RepeatSubmit;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.gxgz.common.log.annotation.Log;
import com.gxgz.common.web.core.BaseController;
import com.gxgz.common.mybatis.core.page.PageQuery;
import com.gxgz.common.core.domain.R;
import com.gxgz.common.core.validate.AddGroup;
import com.gxgz.common.core.validate.EditGroup;
import com.gxgz.common.log.enums.BusinessType;
import com.gxgz.basicdata.domain.vo.GzAreanameVo;
import com.gxgz.basicdata.domain.bo.GzAreanameBo;
import com.gxgz.basicdata.service.IGzAreanameService;
import com.gxgz.common.mybatis.core.page.TableDataInfo;

/**
 * 地区名称
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basicdata/areaname")
public class GzAreanameController extends BaseController {

    private final IGzAreanameService gzAreanameService;

    /**
     * 查询地区名称列表
     */
//    @SaCheckPermission("basicdata:areaname:list")
    @GetMapping("/list")
    public TableDataInfo<GzAreanameVo> list(GzAreanameBo bo, PageQuery pageQuery) {
        return gzAreanameService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出地区名称列表
     */
//    @SaCheckPermission("basicdata:areaname:export")
    @Log(title = "地区名称", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GzAreanameBo bo, HttpServletResponse response) {
        List<GzAreanameVo> list = gzAreanameService.queryList(bo);
        ExcelUtil.exportExcel(list, "地区名称", GzAreanameVo.class, response);
    }

    /**
     * 获取地区名称详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("basicdata:areaname:query")
    @GetMapping("/{id}")
    public R<GzAreanameVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(gzAreanameService.queryById(id));
    }

    /**
     * 新增地区名称
     */
//    @SaCheckPermission("basicdata:areaname:add")
    @Log(title = "地区名称", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GzAreanameBo bo) {
        return toAjax(gzAreanameService.insertByBo(bo));
    }

    /**
     * 修改地区名称
     */
//    @SaCheckPermission("basicdata:areaname:edit")
    @Log(title = "地区名称", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GzAreanameBo bo) {
        return toAjax(gzAreanameService.updateByBo(bo));
    }

    /**
     * 删除地区名称
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("basicdata:areaname:remove")
    @Log(title = "地区名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(gzAreanameService.deleteWithValidByIds(List.of(ids), true));
    }


//    @SaCheckPermission("basicdata:areaname:edit")
    @Log(title = "更新显示状态", businessType = BusinessType.UPDATE)
    @PostMapping("/updateShowStatus/{ids}")
    public R<Void> updateShowStatus(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids, String status) {
        return toAjax(gzAreanameService.updateShowStatus(List.of(ids), status));
    }

//    @SaCheckPermission("basicdata:areaname:add")
    @PostMapping("/initBaseData")
    public R<Void> initBaseData() throws IOException {
//        return toAjax(gzAreanameService.initBaseData(true));
        return toAjax(true);
    }

}
